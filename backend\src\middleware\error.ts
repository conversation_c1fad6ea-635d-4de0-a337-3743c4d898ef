import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { ZodError } from 'zod'

export const errorHandler = (err: Error, c: Context) => {
console.error('Error:', err)

if (err instanceof HTTPException) {
return c.json({ error: err.message }, err.status)
}

if (err instanceof ZodError) {
return c.json({
error: 'Validation error',
details: err.errors.map(e => ({
path: e.path.join('.'),
message: e.message
}))
}, 400)
}

// Prisma errors
if (err.message.includes('Unique constraint failed')) {
return c.json({ error: 'Record already exists' }, 409)
}

if (err.message.includes('Record to update not found')) {
return c.json({ error: 'Record not found' }, 404)
}

// Default error
return c.json({
error: 'Internal server error',
...(process.env.NODE_ENV === 'development' && { details: err.message })
}, 500)
}