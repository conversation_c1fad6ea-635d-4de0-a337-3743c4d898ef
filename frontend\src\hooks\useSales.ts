import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/api';
import { Sale, SaleFilters, PaginatedResponse, SaleItem } from '../types';

// Sale API functions
const saleApi = {
  getSales: async (filters: SaleFilters = {}): Promise<PaginatedResponse<Sale>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/sales?${params.toString()}`);
    return response.data;
  },

  getSale: async (id: string): Promise<Sale> => {
    const response = await api.get(`/sales/${id}`);
    return response.data;
  },

  createSale: async (saleData: {
    customerId?: string;
    items: Array<{
      productId: string;
      quantity: number;
      unitPrice: number;
      discount?: number;
    }>;
    paymentMethod: string;
    discount?: number;
    tax?: number;
    pointsUsed?: number;
    notes?: string;
  }): Promise<Sale> => {
    const response = await api.post('/sales', saleData);
    return response.data;
  },

  updateSale: async ({ id, ...saleData }: Partial<Sale> & { id: string }): Promise<Sale> => {
    const response = await api.put(`/sales/${id}`, saleData);
    return response.data;
  },

  deleteSale: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/sales/${id}`);
    return response.data;
  },

  refundSale: async (id: string, data: {
    reason: string;
    amount?: number;
    items?: Array<{
      saleItemId: string;
      quantity: number;
    }>;
  }): Promise<{ message: string }> => {
    const response = await api.post(`/sales/${id}/refund`, data);
    return response.data;
  },

  getSaleStats: async (params: {
    startDate?: string;
    endDate?: string;
    userId?: string;
  } = {}): Promise<{
    totalSales: number;
    totalRevenue: number;
    averageOrderValue: number;
    topProducts: Array<{
      productId: string;
      productName: string;
      quantity: number;
      revenue: number;
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/sales/stats?${queryParams.toString()}`);
    return response.data;
  },

  getDailySales: async (params: {
    startDate?: string;
    endDate?: string;
  } = {}): Promise<Array<{
    date: string;
    totalSales: number;
    totalRevenue: number;
    orderCount: number;
  }>> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/sales/daily?${queryParams.toString()}`);
    return response.data;
  },

  printReceipt: async (id: string): Promise<{ receiptUrl: string }> => {
    const response = await api.get(`/sales/${id}/receipt`);
    return response.data;
  },

  emailReceipt: async (id: string, email: string): Promise<{ message: string }> => {
    const response = await api.post(`/sales/${id}/email-receipt`, { email });
    return response.data;
  },
};

// Sale hooks
export const useSales = (filters: SaleFilters = {}) => {
  return useQuery({
    queryKey: ['sales', filters],
    queryFn: () => saleApi.getSales(filters),
  });
};

export const useSale = (id: string) => {
  return useQuery({
    queryKey: ['sale', id],
    queryFn: () => saleApi.getSale(id),
    enabled: !!id,
  });
};

export const useCreateSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: saleApi.createSale,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      queryClient.invalidateQueries({ queryKey: ['products'] }); // Update stock
      queryClient.invalidateQueries({ queryKey: ['customers'] }); // Update customer stats
      queryClient.invalidateQueries({ queryKey: ['dashboard'] }); // Update dashboard
    },
  });
};

export const useUpdateSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: saleApi.updateSale,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      queryClient.invalidateQueries({ queryKey: ['sale', data.id] });
    },
  });
};

export const useDeleteSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: saleApi.deleteSale,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      queryClient.invalidateQueries({ queryKey: ['products'] }); // Update stock
      queryClient.invalidateQueries({ queryKey: ['customers'] }); // Update customer stats
    },
  });
};

export const useRefundSale = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, ...data }: { id: string } & Parameters<typeof saleApi.refundSale>[1]) =>
      saleApi.refundSale(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      queryClient.invalidateQueries({ queryKey: ['sale', id] });
      queryClient.invalidateQueries({ queryKey: ['products'] }); // Update stock
      queryClient.invalidateQueries({ queryKey: ['customers'] }); // Update customer stats
    },
  });
};

export const useSaleStats = (params: {
  startDate?: string;
  endDate?: string;
  userId?: string;
} = {}) => {
  return useQuery({
    queryKey: ['sales', 'stats', params],
    queryFn: () => saleApi.getSaleStats(params),
  });
};

export const useDailySales = (params: {
  startDate?: string;
  endDate?: string;
} = {}) => {
  return useQuery({
    queryKey: ['sales', 'daily', params],
    queryFn: () => saleApi.getDailySales(params),
  });
};

export const usePrintReceipt = () => {
  return useMutation({
    mutationFn: saleApi.printReceipt,
  });
};

export const useEmailReceipt = () => {
  return useMutation({
    mutationFn: ({ id, email }: { id: string; email: string }) =>
      saleApi.emailReceipt(id, email),
  });
};
