import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/api';

interface Setting {
  id: string;
  key: string;
  value: string;
  type: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON';
  createdAt: string;
  updatedAt: string;
}

// Settings API functions
const settingsApi = {
  getSettings: async (): Promise<Setting[]> => {
    const response = await api.get('/settings');
    return response.data;
  },

  getSetting: async (key: string): Promise<Setting> => {
    const response = await api.get(`/settings/${key}`);
    return response.data;
  },

  updateSetting: async (key: string, value: string): Promise<Setting> => {
    const response = await api.put(`/settings/${key}`, { value });
    return response.data;
  },

  updateMultipleSettings: async (settings: Array<{ key: string; value: string }>): Promise<{ message: string }> => {
    const response = await api.put('/settings/bulk', { settings });
    return response.data;
  },

  getBusinessSettings: async (): Promise<{
    businessName: string;
    businessAddress: string;
    businessPhone: string;
    businessEmail: string;
    taxRate: number;
    currency: string;
    timezone: string;
    loyaltyPointsRate: number;
    loyaltyPointsValue: number;
  }> => {
    const response = await api.get('/settings/business');
    return response.data;
  },

  updateBusinessSettings: async (settings: {
    businessName?: string;
    businessAddress?: string;
    businessPhone?: string;
    businessEmail?: string;
    taxRate?: number;
    currency?: string;
    timezone?: string;
    loyaltyPointsRate?: number;
    loyaltyPointsValue?: number;
  }): Promise<{ message: string }> => {
    const response = await api.put('/settings/business', settings);
    return response.data;
  },

  getNotificationSettings: async (): Promise<{
    emailNotifications: boolean;
    smsNotifications: boolean;
    lowStockAlerts: boolean;
    dailyReports: boolean;
    weeklyReports: boolean;
    monthlyReports: boolean;
  }> => {
    const response = await api.get('/settings/notifications');
    return response.data;
  },

  updateNotificationSettings: async (settings: {
    emailNotifications?: boolean;
    smsNotifications?: boolean;
    lowStockAlerts?: boolean;
    dailyReports?: boolean;
    weeklyReports?: boolean;
    monthlyReports?: boolean;
  }): Promise<{ message: string }> => {
    const response = await api.put('/settings/notifications', settings);
    return response.data;
  },

  getPaymentSettings: async (): Promise<{
    acceptCash: boolean;
    acceptCreditCard: boolean;
    acceptDebitCard: boolean;
    acceptPaypal: boolean;
    acceptStripe: boolean;
    acceptLoyaltyPoints: boolean;
    stripePublicKey?: string;
    paypalClientId?: string;
  }> => {
    const response = await api.get('/settings/payment');
    return response.data;
  },

  updatePaymentSettings: async (settings: {
    acceptCash?: boolean;
    acceptCreditCard?: boolean;
    acceptDebitCard?: boolean;
    acceptPaypal?: boolean;
    acceptStripe?: boolean;
    acceptLoyaltyPoints?: boolean;
    stripePublicKey?: string;
    stripeSecretKey?: string;
    paypalClientId?: string;
    paypalClientSecret?: string;
  }): Promise<{ message: string }> => {
    const response = await api.put('/settings/payment', settings);
    return response.data;
  },

  getInventorySettings: async (): Promise<{
    autoReorder: boolean;
    reorderThreshold: number;
    trackExpiry: boolean;
    expiryAlertDays: number;
    barcodeGeneration: boolean;
    stockValuationMethod: 'FIFO' | 'LIFO' | 'AVERAGE';
  }> => {
    const response = await api.get('/settings/inventory');
    return response.data;
  },

  updateInventorySettings: async (settings: {
    autoReorder?: boolean;
    reorderThreshold?: number;
    trackExpiry?: boolean;
    expiryAlertDays?: number;
    barcodeGeneration?: boolean;
    stockValuationMethod?: 'FIFO' | 'LIFO' | 'AVERAGE';
  }): Promise<{ message: string }> => {
    const response = await api.put('/settings/inventory', settings);
    return response.data;
  },
};

// Settings hooks
export const useSettings = () => {
  return useQuery({
    queryKey: ['settings'],
    queryFn: settingsApi.getSettings,
  });
};

export const useSetting = (key: string) => {
  return useQuery({
    queryKey: ['setting', key],
    queryFn: () => settingsApi.getSetting(key),
    enabled: !!key,
  });
};

export const useUpdateSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ key, value }: { key: string; value: string }) =>
      settingsApi.updateSetting(key, value),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      queryClient.invalidateQueries({ queryKey: ['setting', data.key] });
    },
  });
};

export const useUpdateMultipleSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: settingsApi.updateMultipleSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};

export const useBusinessSettings = () => {
  return useQuery({
    queryKey: ['settings', 'business'],
    queryFn: settingsApi.getBusinessSettings,
  });
};

export const useUpdateBusinessSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: settingsApi.updateBusinessSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'business'] });
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};

export const useNotificationSettings = () => {
  return useQuery({
    queryKey: ['settings', 'notifications'],
    queryFn: settingsApi.getNotificationSettings,
  });
};

export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: settingsApi.updateNotificationSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'notifications'] });
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};

export const usePaymentSettings = () => {
  return useQuery({
    queryKey: ['settings', 'payment'],
    queryFn: settingsApi.getPaymentSettings,
  });
};

export const useUpdatePaymentSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: settingsApi.updatePaymentSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'payment'] });
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};

export const useInventorySettings = () => {
  return useQuery({
    queryKey: ['settings', 'inventory'],
    queryFn: settingsApi.getInventorySettings,
  });
};

export const useUpdateInventorySettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: settingsApi.updateInventorySettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'inventory'] });
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
  });
};
