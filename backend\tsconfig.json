{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "lib": ["ES2020", "DOM", "DOM.Iterable"], "types": ["@types/node"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules"]}