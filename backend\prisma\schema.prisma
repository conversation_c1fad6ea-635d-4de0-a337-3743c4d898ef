generator client {
provider = "prisma-client-js"
}

datasource db {
provider = "postgresql"
url = env("DATABASE_URL")
}

model User {
id String @id @default(cuid())
email String @unique
password String
firstName String
lastName String
role UserRole @default(STAFF)
isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
sales Sale[]
auditLogs AuditLog[]
createdProducts Product[] @relation("CreatedBy")

@@map("users")
}

model Customer {
id String @id @default(cuid())
email String? @unique
phone String?
firstName String
lastName String
dateOfBirth DateTime?
address String?
city String?
state String?
zipCode String?
country String?

// Loyalty & Analytics
loyaltyPoints Int @default(0)
loyaltyTier LoyaltyTier @default(BRONZE)
totalSpent Decimal @default(0) @db.Decimal(10,2)
totalOrders Int @default(0)
lastPurchaseAt DateTime?
averageOrderValue Decimal @default(0) @db.Decimal(10,2)

// AI Insights
lifetimeValue Decimal? @db.Decimal(10,2)
churnRisk ChurnRisk @default(LOW)
segment String?

isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
sales Sale[]

@@map("customers")
}

model Category {
id String @id @default(cuid())
name String
description String?
slug String @unique
imageUrl String?
isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
products Product[]

@@map("categories")
}

model Product {
id String @id @default(cuid())
name String
description String?
sku String @unique
barcode String? @unique
price Decimal @db.Decimal(10,2)
cost Decimal @db.Decimal(10,2)
markup Decimal @db.Decimal(5,2) @default(0)

// Inventory
stockQuantity Int @default(0)
minStockLevel Int @default(0)
maxStockLevel Int?
reorderPoint Int @default(0)

// Product Info
weight Decimal? @db.Decimal(8,2)
dimensions String?
imageUrl String?
tags String[]

// AI Features
aiGenerated Boolean @default(false)
salesForecast Json?
performanceScore Decimal? @db.Decimal(3,2)

// Status
isActive Boolean @default(true)
isFeatured Boolean @default(false)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
createdById String?

// Relations
categoryId String?
category Category? @relation(fields: [categoryId], references: [id])
createdBy User? @relation("CreatedBy", fields: [createdById], references: [id])
saleItems SaleItem[]
inventoryLogs InventoryLog[]
suppliers ProductSupplier[]
purchaseOrderItems PurchaseOrderItem[]

@@map("products")
}

model Supplier {
id String @id @default(cuid())
name String
email String?
phone String?
address String?
city String?
state String?
zipCode String?
country String?

contactPerson String?
website String?
notes String?

isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
products ProductSupplier[]
purchaseOrders PurchaseOrder[]

@@map("suppliers")
}

model ProductSupplier {
id String @id @default(cuid())
productId String
supplierId String
supplierSku String?
leadTime Int? // days
minOrderQty Int @default(1)
unitCost Decimal @db.Decimal(10,2)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
supplier Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)

@@unique([productId, supplierId])
@@map("product_suppliers")
}

model PurchaseOrder {
id String @id @default(cuid())
orderNumber String @unique
supplierId String
status PurchaseOrderStatus @default(DRAFT)

orderDate DateTime @default(now())
expectedDate DateTime?
receivedDate DateTime?

subtotal Decimal @db.Decimal(10,2)
tax Decimal @db.Decimal(10,2) @default(0)
total Decimal @db.Decimal(10,2)

notes String?

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
supplier Supplier @relation(fields: [supplierId], references: [id])
items PurchaseOrderItem[]

@@map("purchase_orders")
}

model PurchaseOrderItem {
id String @id @default(cuid())
purchaseOrderId String
productId String

quantity Int
unitCost Decimal @db.Decimal(10,2)
totalCost Decimal @db.Decimal(10,2)

receivedQty Int @default(0)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
purchaseOrder PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
product Product @relation(fields: [productId], references: [id])

@@map("purchase_order_items")
}

model Sale {
id String @id @default(cuid())
saleNumber String @unique
customerId String?
userId String

// Sale Details
subtotal Decimal @db.Decimal(10,2)
tax Decimal @db.Decimal(10,2) @default(0)
discount Decimal @db.Decimal(10,2) @default(0)
total Decimal @db.Decimal(10,2)

// Payment
paymentMethod PaymentMethod
paymentStatus PaymentStatus @default(COMPLETED)

// Loyalty
pointsEarned Int @default(0)
pointsUsed Int @default(0)

notes String?

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
customer Customer? @relation(fields: [customerId], references: [id])
user User @relation(fields: [userId], references: [id])
items SaleItem[]

@@map("sales")
}

model SaleItem {
id String @id @default(cuid())
saleId String
productId String

quantity Int
unitPrice Decimal @db.Decimal(10,2)
discount Decimal @db.Decimal(10,2) @default(0)
total Decimal @db.Decimal(10,2)

createdAt DateTime @default(now())

// Relations
sale Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)
product Product @relation(fields: [productId], references: [id])

@@map("sale_items")
}

model InventoryLog {
id String @id @default(cuid())
productId String
type InventoryLogType
quantity Int
previousQty Int
newQty Int

reason String?
reference String? // PO number, sale number, etc.

createdAt DateTime @default(now())

// Relations
product Product @relation(fields: [productId], references: [id])

@@map("inventory_logs")
}

model AuditLog {
id String @id @default(cuid())
userId String?
action String
entity String // table name
entityId String
oldValues Json?
newValues Json?
ipAddress String?
userAgent String?

createdAt DateTime @default(now())

// Relations
user User? @relation(fields: [userId], references: [id])

@@map("audit_logs")
}

model Setting {
id String @id @default(cuid())
key String @unique
value String
type SettingType @default(STRING)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

@@map("settings")
}

// Enums
enum UserRole {
ADMIN
MANAGER
STAFF
}

enum LoyaltyTier {
BRONZE
SILVER
GOLD
PLATINUM
}

enum ChurnRisk {
LOW
MEDIUM
HIGH
CRITICAL
}

enum PaymentMethod {
CASH
CREDIT_CARD
DEBIT_CARD
PAYPAL
STRIPE
LOYALTY_POINTS
}

enum PaymentStatus {
PENDING
COMPLETED
FAILED
REFUNDED
}

enum PurchaseOrderStatus {
DRAFT
SENT
CONFIRMED
RECEIVED
CANCELLED
}

enum InventoryLogType {
PURCHASE
SALE
ADJUSTMENT
RETURN
DAMAGED
EXPIRED
}

enum SettingType {
STRING
NUMBER
BOOLEAN
JSON
}