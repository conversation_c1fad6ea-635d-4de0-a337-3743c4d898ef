import { Context, Next } from 'hono'
import { HTTPException } from 'hono/http-exception'
import jwt from 'jsonwebtoken'
import { prisma } from '../lib/db'
import { UserRole } from '@prisma/client'

interface JWTPayload {
userId: string
email: string
role: UserRole
}

declare module 'hono' {
interface ContextVariableMap {
user: {
id: string
email: string
role: UserRole
firstName: string
lastName: string
}
}
}

export const authenticateToken = async (c: Context, next: Next) => {
try {
const authHeader = c.req.header('Authorization')
const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      throw new HTTPException(401, { message: 'Access token required' })
    }

    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new HTTPException(500, { message: 'JWT secret not configured' })
    }

    const decoded = jwt.verify(token, secret) as JWTPayload

    // Get fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!user) {
      throw new HTTPException(401, { message: 'User not found' })
    }

    if (!user.isActive) {
      throw new HTTPException(401, { message: 'Account is disabled' })
    }

    c.set('user', user)
    await next()

} catch (error) {
if (error instanceof jwt.JsonWebTokenError) {
throw new HTTPException(401, { message: 'Invalid token' })
}
if (error instanceof jwt.TokenExpiredError) {
throw new HTTPException(401, { message: 'Token expired' })
}
throw error
}
}

export const requireRole = (roles: UserRole[]) => {
return async (c: Context, next: Next) => {
const user = c.get('user')

    if (!user) {
      throw new HTTPException(401, { message: 'Authentication required' })
    }

    if (!roles.includes(user.role)) {
      throw new HTTPException(403, { message: 'Insufficient permissions' })
    }

    await next()

}
}

export const requireAdmin = requireRole([UserRole.ADMIN])
export const requireManager = requireRole([UserRole.ADMIN, UserRole.MANAGER])
export const requireStaff = requireRole([UserRole.ADMIN, UserRole.MANAGER, UserRole.STAFF])