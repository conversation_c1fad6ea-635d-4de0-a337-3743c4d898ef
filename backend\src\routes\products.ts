import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { productSchema } from '../lib/validation'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { auditLogger } from '../middleware/logging'
import { InventoryLogType } from '@prisma/client'

export const productRoutes = new Hono()

// Get all products with filters and pagination
productRoutes.get('/', authenticateToken, requireStaff, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const search = c.req.query('search') || ''
const categoryId = c.req.query('categoryId')
const isActive = c.req.query('isActive')
const lowStock = c.req.query('lowStock') === 'true'

const skip = (page - 1) * limit

const where = {
...(search && {
OR: [
{ name: { contains: search, mode: 'insensitive' as const } },
{ sku: { contains: search, mode: 'insensitive' as const } },
{ barcode: { contains: search, mode: 'insensitive' as const } },
{ description: { contains: search, mode: 'insensitive' as const } }
]
}),
...(categoryId && { categoryId }),
...(isActive !== undefined && { isActive: isActive === 'true' }),
...(lowStock && {
stockQuantity: { lte: prisma.raw('min_stock_level') }
})
}

const [products, total] = await Promise.all([
prisma.product.findMany({
where,
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
},
_count: {
select: { saleItems: true }
}
},
orderBy: { createdAt: 'desc' },
skip,
take: limit
}),
prisma.product.count({ where })
])

return c.json({
products,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Get single product
productRoutes.get('/:id', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')

const product = await prisma.product.findUnique({
where: { id },
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
},
suppliers: {
include: { supplier: true }
},
inventoryLogs: {
orderBy: { createdAt: 'desc' },
take: 10
},
saleItems: {
include: { sale: true },
orderBy: { createdAt: 'desc' },
take: 10
}
}
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

return c.json({ product })
})

// Create product
productRoutes.post('/', authenticateToken, requireManager, zValidator('json', productSchema), auditLogger('CREATE', 'product', ''), async (c) => {
const data = c.req.valid('json')
const user = c.get('user')

// Calculate markup percentage
const markup = ((data.price - data.cost) / data.cost) * 100

const product = await prisma.product.create({
data: {
...data,
markup,
createdById: user.id
},
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
}
}
})

// Create initial inventory log
if (data.stockQuantity > 0) {
await prisma.inventoryLog.create({
data: {
productId: product.id,
type: InventoryLogType.ADJUSTMENT,
quantity: data.stockQuantity,
previousQty: 0,
newQty: data.stockQuantity,
reason: 'Initial stock'
}
})
}

return c.json({
message: 'Product created successfully',
product
}, 201)
})

// Update product
productRoutes.put('/:id', authenticateToken, requireManager, zValidator('json', productSchema.partial()), auditLogger('UPDATE', 'product', ''), async (c) => {
const id = c.req.param('id')
const data = c.req.valid('json')

const existingProduct = await prisma.product.findUnique({
where: { id }
})

if (!existingProduct) {
throw new HTTPException(404, { message: 'Product not found' })
}

// Calculate markup if price or cost changed
let markup = existingProduct.markup
if (data.price || data.cost) {
const newPrice = data.price || existingProduct.price
const newCost = data.cost || existingProduct.cost
markup = ((Number(newPrice) - Number(newCost)) / Number(newCost)) * 100
}

const product = await prisma.product.update({
where: { id },
data: {
...data,
markup
},
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
}
}
})

// Create inventory log if stock quantity changed
if (data.stockQuantity !== undefined && data.stockQuantity !== existingProduct.stockQuantity) {
await prisma.inventoryLog.create({
data: {
productId: id,
type: InventoryLogType.ADJUSTMENT,
quantity: data.stockQuantity - existingProduct.stockQuantity,
previousQty: existingProduct.stockQuantity,
newQty: data.stockQuantity,
reason: 'Manual adjustment'
}
})
}

return c.json({
message: 'Product updated successfully',
product
})
})

// Delete product (soft delete)
productRoutes.delete('/:id', authenticateToken, requireManager, auditLogger('DELETE', 'product', ''), async (c) => {
const id = c.req.param('id')

const product = await prisma.product.findUnique({
where: { id }
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

await prisma.product.update({
where: { id },
data: { isActive: false }
})

return c.json({ message: 'Product deleted successfully' })
})

// Bulk update products
productRoutes.post('/bulk-update', authenticateToken, requireManager, zValidator('json', z.object({
productIds: z.array(z.string()),
updates: z.object({
categoryId: z.string().optional(),
isActive: z.boolean().optional(),
isFeatured: z.boolean().optional(),
tags: z.array(z.string()).optional()
})
})), async (c) => {
const { productIds, updates } = c.req.valid('json')

await prisma.product.updateMany({
where: { id: { in: productIds } },
data: updates
})

return c.json({
message: `${productIds.length} products updated successfully`
})
})

// Get products by barcode
productRoutes.get('/barcode/:barcode', authenticateToken, requireStaff, async (c) => {
const barcode = c.req.param('barcode')

const product = await prisma.product.findUnique({
where: { barcode },
include: {
category: true
}
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

return c.json({ product })
})

// Get low stock products
productRoutes.get('/alerts/low-stock', authenticateToken, requireStaff, async (c) => {
const products = await prisma.product.findMany({
where: {
isActive: true,
stockQuantity: { lte: prisma.raw('min_stock_level') }
},
include: {
category: true
},
orderBy: [
{ stockQuantity: 'asc' },
{ name: 'asc' }
]
})

return c.json({ products })
})

// Adjust stock
productRoutes.post('/:id/adjust-stock', authenticateToken, requireManager, zValidator('json', z.object({
quantity: z.number().int(),
reason: z.string().min(1, 'Reason is required'),
type: z.nativeEnum(InventoryLogType).optional().default(InventoryLogType.ADJUSTMENT)
})), async (c) => {
const id = c.req.param('id')
const { quantity, reason, type } = c.req.valid('json')

const product = await prisma.product.findUnique({
where: { id }
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

const newQuantity = Math.max(0, product.stockQuantity + quantity)

await prisma.$transaction(async (tx) => {
await tx.product.update({
where: { id },
data: { stockQuantity: newQuantity }
})

    await tx.inventoryLog.create({
      data: {
        productId: id,
        type,
        quantity,
        previousQty: product.stockQuantity,
        newQty: newQuantity,
        reason
      }
    })

})

return c.json({
message: 'Stock adjusted successfully',
newQuantity
})
})