import { Context, Next } from 'hono'
import { prisma } from '../lib/db'

export const requestLogger = async (c: Context, next: Next) => {
const start = Date.now()
await next()
const ms = Date.now() - start

console.log(`${c.req.method} ${c.req.path} - ${c.res.status} (${ms}ms)`)
}

export const auditLogger = (action: string, entity: string, entityId: string) => {
return async (c: Context, next: Next) => {
await next()

    // Only log successful operations
    if (c.res.status < 400) {
      const user = c.get('user')
      const userAgent = c.req.header('User-Agent')
      const forwarded = c.req.header('X-Forwarded-For')
      const realIp = c.req.header('X-Real-IP')
      const ipAddress = forwarded?.split(',')[0] || realIp || 'unknown'

      try {
        await prisma.auditLog.create({
          data: {
            userId: user?.id,
            action,
            entity,
            entityId,
            ipAddress,
            userAgent: userAgent || 'unknown'
          }
        })
      } catch (error) {
        console.error('Failed to create audit log:', error)
      }
    }

}
}