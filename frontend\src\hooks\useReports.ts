import { useQuery, useMutation } from '@tanstack/react-query';
import { api } from '../lib/api';

// Reports API functions
const reportsApi = {
  getSalesReport: async (params: {
    startDate: string;
    endDate: string;
    groupBy?: 'day' | 'week' | 'month';
    userId?: string;
    customerId?: string;
    productId?: string;
  }): Promise<{
    summary: {
      totalSales: number;
      totalRevenue: number;
      averageOrderValue: number;
      totalOrders: number;
    };
    data: Array<{
      period: string;
      sales: number;
      revenue: number;
      orders: number;
    }>;
    topProducts: Array<{
      productId: string;
      productName: string;
      quantity: number;
      revenue: number;
    }>;
    topCustomers: Array<{
      customerId: string;
      customerName: string;
      totalSpent: number;
      totalOrders: number;
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/reports/sales?${queryParams.toString()}`);
    return response.data;
  },

  getInventoryReport: async (params: {
    categoryId?: string;
    lowStock?: boolean;
    outOfStock?: boolean;
  } = {}): Promise<{
    summary: {
      totalProducts: number;
      totalValue: number;
      lowStockItems: number;
      outOfStockItems: number;
    };
    products: Array<{
      id: string;
      name: string;
      sku: string;
      category: string;
      currentStock: number;
      minStockLevel: number;
      value: number;
      status: 'in_stock' | 'low_stock' | 'out_of_stock';
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/reports/inventory?${queryParams.toString()}`);
    return response.data;
  },

  getCustomerReport: async (params: {
    startDate?: string;
    endDate?: string;
    loyaltyTier?: string;
    churnRisk?: string;
  } = {}): Promise<{
    summary: {
      totalCustomers: number;
      newCustomers: number;
      activeCustomers: number;
      averageLifetimeValue: number;
    };
    customers: Array<{
      id: string;
      name: string;
      email: string;
      totalSpent: number;
      totalOrders: number;
      lastPurchase: string;
      loyaltyTier: string;
      churnRisk: string;
    }>;
    loyaltyDistribution: Array<{
      tier: string;
      count: number;
      percentage: number;
    }>;
    churnAnalysis: Array<{
      risk: string;
      count: number;
      percentage: number;
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/reports/customers?${queryParams.toString()}`);
    return response.data;
  },

  getProductPerformanceReport: async (params: {
    startDate: string;
    endDate: string;
    categoryId?: string;
    limit?: number;
  }): Promise<{
    topPerformers: Array<{
      id: string;
      name: string;
      sku: string;
      category: string;
      totalSold: number;
      revenue: number;
      profit: number;
      margin: number;
    }>;
    underPerformers: Array<{
      id: string;
      name: string;
      sku: string;
      category: string;
      totalSold: number;
      revenue: number;
      daysInStock: number;
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/reports/product-performance?${queryParams.toString()}`);
    return response.data;
  },

  getProfitLossReport: async (params: {
    startDate: string;
    endDate: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<{
    summary: {
      totalRevenue: number;
      totalCost: number;
      grossProfit: number;
      grossMargin: number;
    };
    data: Array<{
      period: string;
      revenue: number;
      cost: number;
      profit: number;
      margin: number;
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/reports/profit-loss?${queryParams.toString()}`);
    return response.data;
  },

  exportReport: async (reportType: string, params: any, format: 'pdf' | 'excel' | 'csv'): Promise<{ downloadUrl: string }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    queryParams.append('format', format);
    
    const response = await api.get(`/reports/${reportType}/export?${queryParams.toString()}`);
    return response.data;
  },
};

// Reports hooks
export const useSalesReport = (params: {
  startDate: string;
  endDate: string;
  groupBy?: 'day' | 'week' | 'month';
  userId?: string;
  customerId?: string;
  productId?: string;
}) => {
  return useQuery({
    queryKey: ['reports', 'sales', params],
    queryFn: () => reportsApi.getSalesReport(params),
    enabled: !!(params.startDate && params.endDate),
  });
};

export const useInventoryReport = (params: {
  categoryId?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
} = {}) => {
  return useQuery({
    queryKey: ['reports', 'inventory', params],
    queryFn: () => reportsApi.getInventoryReport(params),
  });
};

export const useCustomerReport = (params: {
  startDate?: string;
  endDate?: string;
  loyaltyTier?: string;
  churnRisk?: string;
} = {}) => {
  return useQuery({
    queryKey: ['reports', 'customers', params],
    queryFn: () => reportsApi.getCustomerReport(params),
  });
};

export const useProductPerformanceReport = (params: {
  startDate: string;
  endDate: string;
  categoryId?: string;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ['reports', 'product-performance', params],
    queryFn: () => reportsApi.getProductPerformanceReport(params),
    enabled: !!(params.startDate && params.endDate),
  });
};

export const useProfitLossReport = (params: {
  startDate: string;
  endDate: string;
  groupBy?: 'day' | 'week' | 'month';
}) => {
  return useQuery({
    queryKey: ['reports', 'profit-loss', params],
    queryFn: () => reportsApi.getProfitLossReport(params),
    enabled: !!(params.startDate && params.endDate),
  });
};

export const useExportReport = () => {
  return useMutation({
    mutationFn: ({ reportType, params, format }: {
      reportType: string;
      params: any;
      format: 'pdf' | 'excel' | 'csv';
    }) => reportsApi.exportReport(reportType, params, format),
  });
};
