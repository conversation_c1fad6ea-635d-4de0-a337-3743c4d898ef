import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';

// Dashboard API functions
const dashboardApi = {
  getDashboardStats: async (params: {
    period?: 'today' | 'week' | 'month' | 'year';
    startDate?: string;
    endDate?: string;
  } = {}): Promise<{
    totalSales: number;
    totalRevenue: number;
    totalCustomers: number;
    totalProducts: number;
    lowStockProducts: number;
    pendingOrders: number;
    revenueGrowth: number;
    salesGrowth: number;
    customerGrowth: number;
    topProducts: Array<{
      id: string;
      name: string;
      sales: number;
      revenue: number;
    }>;
    topCustomers: Array<{
      id: string;
      name: string;
      totalSpent: number;
      totalOrders: number;
    }>;
    recentSales: Array<{
      id: string;
      saleNumber: string;
      customerName: string;
      total: number;
      createdAt: string;
    }>;
    salesChart: Array<{
      date: string;
      sales: number;
      revenue: number;
    }>;
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/dashboard/stats?${queryParams.toString()}`);
    return response.data;
  },

  getInventoryAlerts: async (): Promise<{
    lowStock: Array<{
      id: string;
      name: string;
      sku: string;
      currentStock: number;
      minStockLevel: number;
    }>;
    outOfStock: Array<{
      id: string;
      name: string;
      sku: string;
    }>;
    expiringSoon: Array<{
      id: string;
      name: string;
      sku: string;
      expiryDate: string;
    }>;
  }> => {
    const response = await api.get('/dashboard/inventory-alerts');
    return response.data;
  },

  getRecentActivity: async (limit: number = 10): Promise<Array<{
    id: string;
    type: 'sale' | 'purchase' | 'customer' | 'product' | 'user';
    action: string;
    description: string;
    user: string;
    timestamp: string;
  }>> => {
    const response = await api.get(`/dashboard/recent-activity?limit=${limit}`);
    return response.data;
  },

  getPerformanceMetrics: async (params: {
    period?: 'today' | 'week' | 'month' | 'year';
  } = {}): Promise<{
    salesPerformance: {
      target: number;
      actual: number;
      percentage: number;
    };
    revenuePerformance: {
      target: number;
      actual: number;
      percentage: number;
    };
    customerAcquisition: {
      target: number;
      actual: number;
      percentage: number;
    };
    inventoryTurnover: {
      ratio: number;
      daysInInventory: number;
    };
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/dashboard/performance?${queryParams.toString()}`);
    return response.data;
  },
};

// Dashboard hooks
export const useDashboardStats = (params: {
  period?: 'today' | 'week' | 'month' | 'year';
  startDate?: string;
  endDate?: string;
} = {}) => {
  return useQuery({
    queryKey: ['dashboard', 'stats', params],
    queryFn: () => dashboardApi.getDashboardStats(params),
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

export const useInventoryAlerts = () => {
  return useQuery({
    queryKey: ['dashboard', 'inventory-alerts'],
    queryFn: dashboardApi.getInventoryAlerts,
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  });
};

export const useRecentActivity = (limit: number = 10) => {
  return useQuery({
    queryKey: ['dashboard', 'recent-activity', limit],
    queryFn: () => dashboardApi.getRecentActivity(limit),
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
};

export const usePerformanceMetrics = (params: {
  period?: 'today' | 'week' | 'month' | 'year';
} = {}) => {
  return useQuery({
    queryKey: ['dashboard', 'performance', params],
    queryFn: () => dashboardApi.getPerformanceMetrics(params),
    refetchInterval: 15 * 60 * 1000, // Refetch every 15 minutes
  });
};
