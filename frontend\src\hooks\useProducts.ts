import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/api';
import { Product, ProductFilters, PaginatedResponse, ApiResponse } from '../types';

// Product API functions
const productApi = {
  getProducts: async (filters: ProductFilters = {}): Promise<PaginatedResponse<Product>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/products?${params.toString()}`);
    return response.data;
  },

  getProduct: async (id: string): Promise<Product> => {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },

  createProduct: async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
    const response = await api.post('/products', productData);
    return response.data;
  },

  updateProduct: async ({ id, ...productData }: Partial<Product> & { id: string }): Promise<Product> => {
    const response = await api.put(`/products/${id}`, productData);
    return response.data;
  },

  deleteProduct: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/products/${id}`);
    return response.data;
  },

  bulkUpdateProducts: async (updates: Array<{ id: string; data: Partial<Product> }>): Promise<{ message: string }> => {
    const response = await api.put('/products/bulk', { updates });
    return response.data;
  },

  getLowStockProducts: async (): Promise<Product[]> => {
    const response = await api.get('/products/low-stock');
    return response.data;
  },

  generateBarcode: async (productId: string): Promise<{ barcode: string }> => {
    const response = await api.post(`/products/${productId}/generate-barcode`);
    return response.data;
  },

  updateStock: async (productId: string, data: {
    quantity: number;
    type: 'PURCHASE' | 'SALE' | 'ADJUSTMENT' | 'RETURN' | 'DAMAGED' | 'EXPIRED';
    reason?: string;
    reference?: string;
  }): Promise<{ message: string }> => {
    const response = await api.put(`/products/${productId}/stock`, data);
    return response.data;
  },
};

// Product hooks
export const useProducts = (filters: ProductFilters = {}) => {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => productApi.getProducts(filters),
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => productApi.getProduct(id),
    enabled: !!id,
  });
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productApi.createProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productApi.updateProduct,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', data.id] });
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productApi.deleteProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

export const useBulkUpdateProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productApi.bulkUpdateProducts,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

export const useLowStockProducts = () => {
  return useQuery({
    queryKey: ['products', 'low-stock'],
    queryFn: productApi.getLowStockProducts,
  });
};

export const useGenerateBarcode = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productApi.generateBarcode,
    onSuccess: (_, productId) => {
      queryClient.invalidateQueries({ queryKey: ['product', productId] });
    },
  });
};

export const useUpdateStock = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, ...data }: { productId: string } & Parameters<typeof productApi.updateStock>[1]) =>
      productApi.updateStock(productId, data),
    onSuccess: (_, { productId }) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', productId] });
      queryClient.invalidateQueries({ queryKey: ['products', 'low-stock'] });
    },
  });
};
