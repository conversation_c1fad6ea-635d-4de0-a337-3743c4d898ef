import { z<PERSON><PERSON>da<PERSON> } from "@hono/zod-validator";
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import z from "zod";
import { generateToken, hashPassword, verifyPassword } from "../lib/auth";
import { prisma } from "../lib/db";
import { loginSchema, registerSchema } from "../lib/validation";
import { authenticateToken, requireAdmin } from "../middleware/auth";

export const authRoutes = new Hono();

// Login
authRoutes.post("/login", zValidator("json", loginSchema), async (c) => {
  const { email, password } = c.req.valid("json");

  const user = await prisma.user.findUnique({
    where: { email },
    select: {
      id: true,
      email: true,
      password: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
    },
  });

  if (!user || !user.isActive) {
    throw new HTTPException(401, { message: "Invalid credentials" });
  }

  const isValidPassword = await verifyPassword(password, user.password);
  if (!isValidPassword) {
    throw new HTTPException(401, { message: "Invalid credentials" });
  }

  const token = generateToken({
    userId: user.id,
    email: user.email,
    role: user.role,
  });

  // Remove password from response
  const { password: _, ...userWithoutPassword } = user;

  return c.json({
    message: "Login successful",
    token,
    user: userWithoutPassword,
  });
});

// Register (Admin only)
authRoutes.post(
  "/register",
  authenticateToken,
  requireAdmin,
  zValidator("json", registerSchema),
  async (c) => {
    const { email, password, firstName, lastName, role } = c.req.valid("json");

    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new HTTPException(409, { message: "User already exists" });
    }

    const hashedPassword = await hashPassword(password);

    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    return c.json(
      {
        message: "User created successfully",
        user,
      },
      201
    );
  }
);

// Get current user profile
authRoutes.get("/me", authenticateToken, async (c) => {
  const user = c.get("user");

  const userDetails = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  return c.json({ user: userDetails });
});

// Refresh token
authRoutes.post("/refresh", authenticateToken, async (c) => {
  const user = c.get("user");

  const token = generateToken({
    userId: user.id,
    email: user.email,
    role: user.role,
  });

  return c.json({
    message: "Token refreshed successfully",
    token,
  });
});

// Change password
authRoutes.post(
  "/change-password",
  authenticateToken,
  zValidator(
    "json",
    z.object({
      currentPassword: z.string().min(1, "Current password is required"),
      newPassword: z
        .string()
        .min(6, "New password must be at least 6 characters"),
    })
  ),
  async (c) => {
    const user = c.get("user");
    const { currentPassword, newPassword } = c.req.valid("json");

    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
    });

    if (!dbUser) {
      throw new HTTPException(404, { message: "User not found" });
    }

    const isValidPassword = await verifyPassword(
      currentPassword,
      dbUser.password
    );
    if (!isValidPassword) {
      throw new HTTPException(400, {
        message: "Current password is incorrect",
      });
    }

    const hashedNewPassword = await hashPassword(newPassword);

    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedNewPassword },
    });

    return c.json({ message: "Password changed successfully" });
  }
);
