{"name": "ecommerce-pos-backend", "version": "1.0.0", "description": "E-commerce POS Backend with Hono, Prisma, PostgreSQL", "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"@hono/node-server": "^1.12.0", "@prisma/client": "^5.15.0", "hono": "^4.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.23.8", "@hono/zod-validator": "^0.2.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "openai": "^4.52.7", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "@types/multer": "^1.4.11"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.10", "@types/cors": "^2.8.17", "@types/node-cron": "^3.0.11", "prisma": "^5.15.0", "tsx": "^4.16.2", "typescript": "^5.5.3"}}