import { serve } from "@hono/node-server";
import dotenv from "dotenv";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSON } from "hono/pretty-json";

import { aiRoutes } from "./routes/ai";
import { auditRoutes } from "./routes/audit";
import { authRoutes } from "./routes/auth";
import { customerRoutes } from "./routes/customers";
import { dashboardRoutes } from "./routes/dashboard";
import { inventoryRoutes } from "./routes/inventory";
import { productRoutes } from "./routes/products";
import { purchaseOrderRoutes } from "./routes/purchase-orders";
import { reportRoutes } from "./routes/reports";
import { saleRoutes } from "./routes/sales";
import { settingRoutes } from "./routes/settings";
import { supplierRoutes } from "./routes/suppliers";
import { userRoutes } from "./routes/users";

import { errorHandler } from "./middleware/error";
import { requestLogger } from "./middleware/logging";

dotenv.config();

const app = new Hono();

// Middleware
app.use("_", logger());
app.use("_", prettyJSON());
app.use(
  "_",
  cors({
    origin:
      process.env.NODE_ENV === "production"
        ? ["https://yourfrontend.com"]
        : ["http://localhost:3000", "http://localhost:3001"],
    credentials: true,
  })
);
app.use("_", requestLogger);

// Health check
app.get("/", (c) => {
  return c.json({
    message: "E-commerce POS API",
    version: "1.0.0",
    status: "healthy",
    timestamp: new Date().toISOString(),
  });
});

app.get("/health", (c) => {
  return c.json({ status: "OK", timestamp: new Date().toISOString() });
});

// Routes
app.route("/api/auth", authRoutes);
app.route("/api/users", userRoutes);
app.route("/api/products", productRoutes);
app.route("/api/customers", customerRoutes);
app.route("/api/sales", saleRoutes);
app.route("/api/inventory", inventoryRoutes);
app.route("/api/suppliers", supplierRoutes);
app.route("/api/purchase-orders", purchaseOrderRoutes);
app.route("/api/dashboard", dashboardRoutes);
app.route("/api/reports", reportRoutes);
app.route("/api/settings", settingRoutes);
app.route("/api/audit", auditRoutes);
app.route("/api/ai", aiRoutes);

// Global error handler
app.onError(errorHandler);

// 404 handler
app.notFound((c) => {
  return c.json({ error: "Not Found", path: c.req.path }, 404);
});

const port = parseInt(process.env.PORT || "3001");

console.log(`🚀 Server starting on port ${port}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV}`);

serve({
  fetch: app.fetch,
  port,
});

export default app;
