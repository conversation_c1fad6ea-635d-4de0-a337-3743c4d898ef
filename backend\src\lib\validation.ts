import { z } from 'zod'
import { UserRole, PaymentMethod, PaymentStatus, LoyaltyTier, ChurnRisk } from '@prisma/client'

export const loginSchema = z.object({
email: z.string().email('Invalid email format'),
password: z.string().min(6, 'Password must be at least 6 characters')
})

export const registerSchema = z.object({
email: z.string().email('Invalid email format'),
password: z.string().min(6, 'Password must be at least 6 characters'),
firstName: z.string().min(1, 'First name is required'),
lastName: z.string().min(1, 'Last name is required'),
role: z.nativeEnum(UserRole).optional().default(UserRole.STAFF)
})

export const customerSchema = z.object({
firstName: z.string().min(1, 'First name is required'),
lastName: z.string().min(1, 'Last name is required'),
email: z.string().email('Invalid email format').optional(),
phone: z.string().optional(),
dateOfBirth: z.string().transform(str => str ? new Date(str) : undefined).optional(),
address: z.string().optional(),
city: z.string().optional(),
state: z.string().optional(),
zipCode: z.string().optional(),
country: z.string().optional(),
loyaltyTier: z.nativeEnum(LoyaltyTier).optional(),
churnRisk: z.nativeEnum(ChurnRisk).optional()
})

export const productSchema = z.object({
name: z.string().min(1, 'Product name is required'),
description: z.string().optional(),
sku: z.string().min(1, 'SKU is required'),
barcode: z.string().optional(),
price: z.number().positive('Price must be positive'),
cost: z.number().positive('Cost must be positive'),
stockQuantity: z.number().int().min(0, 'Stock cannot be negative'),
minStockLevel: z.number().int().min(0, 'Min stock level cannot be negative'),
maxStockLevel: z.number().int().optional(),
reorderPoint: z.number().int().min(0, 'Reorder point cannot be negative'),
weight: z.number().positive().optional(),
dimensions: z.string().optional(),
imageUrl: z.string().url().optional(),
tags: z.array(z.string()).optional(),
categoryId: z.string().optional(),
isFeatured: z.boolean().optional()
})

export const saleSchema = z.object({
customerId: z.string().optional(),
items: z.array(z.object({
productId: z.string(),
quantity: z.number().int().positive('Quantity must be positive'),
unitPrice: z.number().positive('Unit price must be positive'),
discount: z.number().min(0, 'Discount cannot be negative').optional()
})),
paymentMethod: z.nativeEnum(PaymentMethod),
tax: z.number().min(0, 'Tax cannot be negative').optional(),
discount: z.number().min(0, 'Discount cannot be negative').optional(),
pointsUsed: z.number().int().min(0, 'Points used cannot be negative').optional(),
notes: z.string().optional()
})