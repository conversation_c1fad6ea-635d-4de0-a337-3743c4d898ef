import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/api';
import { Customer, CustomerFilters, PaginatedResponse } from '../types';

// Customer API functions
const customerApi = {
  getCustomers: async (filters: CustomerFilters = {}): Promise<PaginatedResponse<Customer>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/customers?${params.toString()}`);
    return response.data;
  },

  getCustomer: async (id: string): Promise<Customer> => {
    const response = await api.get(`/customers/${id}`);
    return response.data;
  },

  createCustomer: async (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'loyaltyPoints' | 'totalSpent' | 'totalOrders' | 'averageOrderValue'>): Promise<Customer> => {
    const response = await api.post('/customers', customerData);
    return response.data;
  },

  updateCustomer: async ({ id, ...customerData }: Partial<Customer> & { id: string }): Promise<Customer> => {
    const response = await api.put(`/customers/${id}`, customerData);
    return response.data;
  },

  deleteCustomer: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/customers/${id}`);
    return response.data;
  },

  getCustomerStats: async (id: string): Promise<{
    totalSpent: number;
    totalOrders: number;
    averageOrderValue: number;
    lastPurchaseAt: string | null;
    loyaltyPoints: number;
    loyaltyTier: string;
  }> => {
    const response = await api.get(`/customers/${id}/stats`);
    return response.data;
  },

  getCustomerPurchaseHistory: async (id: string, params: { page?: number; limit?: number } = {}): Promise<PaginatedResponse<any>> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/customers/${id}/purchases?${queryParams.toString()}`);
    return response.data;
  },

  updateLoyaltyPoints: async (id: string, data: {
    points: number;
    operation: 'ADD' | 'SUBTRACT' | 'SET';
    reason?: string;
  }): Promise<Customer> => {
    const response = await api.put(`/customers/${id}/loyalty-points`, data);
    return response.data;
  },

  getTopCustomers: async (params: { limit?: number; period?: string } = {}): Promise<Customer[]> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/customers/top?${queryParams.toString()}`);
    return response.data;
  },

  searchCustomers: async (query: string): Promise<Customer[]> => {
    const response = await api.get(`/customers/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },
};

// Customer hooks
export const useCustomers = (filters: CustomerFilters = {}) => {
  return useQuery({
    queryKey: ['customers', filters],
    queryFn: () => customerApi.getCustomers(filters),
  });
};

export const useCustomer = (id: string) => {
  return useQuery({
    queryKey: ['customer', id],
    queryFn: () => customerApi.getCustomer(id),
    enabled: !!id,
  });
};

export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: customerApi.createCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
  });
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: customerApi.updateCustomer,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['customer', data.id] });
    },
  });
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: customerApi.deleteCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
  });
};

export const useCustomerStats = (id: string) => {
  return useQuery({
    queryKey: ['customer', id, 'stats'],
    queryFn: () => customerApi.getCustomerStats(id),
    enabled: !!id,
  });
};

export const useCustomerPurchaseHistory = (id: string, params: { page?: number; limit?: number } = {}) => {
  return useQuery({
    queryKey: ['customer', id, 'purchases', params],
    queryFn: () => customerApi.getCustomerPurchaseHistory(id, params),
    enabled: !!id,
  });
};

export const useUpdateLoyaltyPoints = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, ...data }: { id: string } & Parameters<typeof customerApi.updateLoyaltyPoints>[1]) =>
      customerApi.updateLoyaltyPoints(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['customer', data.id] });
      queryClient.invalidateQueries({ queryKey: ['customer', data.id, 'stats'] });
    },
  });
};

export const useTopCustomers = (params: { limit?: number; period?: string } = {}) => {
  return useQuery({
    queryKey: ['customers', 'top', params],
    queryFn: () => customerApi.getTopCustomers(params),
  });
};

export const useSearchCustomers = (query: string) => {
  return useQuery({
    queryKey: ['customers', 'search', query],
    queryFn: () => customerApi.searchCustomers(query),
    enabled: query.length > 0,
  });
};
