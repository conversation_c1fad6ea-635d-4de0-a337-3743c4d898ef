import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/api';
import { Supplier, PaginatedResponse } from '../types';

// Supplier API functions
const supplierApi = {
  getSuppliers: async (params: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  } = {}): Promise<PaginatedResponse<Supplier>> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/suppliers?${queryParams.toString()}`);
    return response.data;
  },

  getSupplier: async (id: string): Promise<Supplier> => {
    const response = await api.get(`/suppliers/${id}`);
    return response.data;
  },

  createSupplier: async (supplierData: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>): Promise<Supplier> => {
    const response = await api.post('/suppliers', supplierData);
    return response.data;
  },

  updateSupplier: async ({ id, ...supplierData }: Partial<Supplier> & { id: string }): Promise<Supplier> => {
    const response = await api.put(`/suppliers/${id}`, supplierData);
    return response.data;
  },

  deleteSupplier: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/suppliers/${id}`);
    return response.data;
  },

  getSupplierProducts: async (id: string, params: {
    page?: number;
    limit?: number;
  } = {}): Promise<PaginatedResponse<any>> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get(`/suppliers/${id}/products?${queryParams.toString()}`);
    return response.data;
  },

  addProductToSupplier: async (supplierId: string, data: {
    productId: string;
    supplierSku?: string;
    leadTime?: number;
    minOrderQty: number;
    unitCost: number;
  }): Promise<{ message: string }> => {
    const response = await api.post(`/suppliers/${supplierId}/products`, data);
    return response.data;
  },

  updateSupplierProduct: async (supplierId: string, productId: string, data: {
    supplierSku?: string;
    leadTime?: number;
    minOrderQty?: number;
    unitCost?: number;
  }): Promise<{ message: string }> => {
    const response = await api.put(`/suppliers/${supplierId}/products/${productId}`, data);
    return response.data;
  },

  removeProductFromSupplier: async (supplierId: string, productId: string): Promise<{ message: string }> => {
    const response = await api.delete(`/suppliers/${supplierId}/products/${productId}`);
    return response.data;
  },

  searchSuppliers: async (query: string): Promise<Supplier[]> => {
    const response = await api.get(`/suppliers/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },
};

// Supplier hooks
export const useSuppliers = (params: {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
} = {}) => {
  return useQuery({
    queryKey: ['suppliers', params],
    queryFn: () => supplierApi.getSuppliers(params),
  });
};

export const useSupplier = (id: string) => {
  return useQuery({
    queryKey: ['supplier', id],
    queryFn: () => supplierApi.getSupplier(id),
    enabled: !!id,
  });
};

export const useCreateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: supplierApi.createSupplier,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
    },
  });
};

export const useUpdateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: supplierApi.updateSupplier,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
      queryClient.invalidateQueries({ queryKey: ['supplier', data.id] });
    },
  });
};

export const useDeleteSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: supplierApi.deleteSupplier,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
    },
  });
};

export const useSupplierProducts = (id: string, params: {
  page?: number;
  limit?: number;
} = {}) => {
  return useQuery({
    queryKey: ['supplier', id, 'products', params],
    queryFn: () => supplierApi.getSupplierProducts(id, params),
    enabled: !!id,
  });
};

export const useAddProductToSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, ...data }: { supplierId: string } & Parameters<typeof supplierApi.addProductToSupplier>[1]) =>
      supplierApi.addProductToSupplier(supplierId, data),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: ['supplier', supplierId, 'products'] });
    },
  });
};

export const useUpdateSupplierProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, productId, ...data }: { supplierId: string; productId: string } & Parameters<typeof supplierApi.updateSupplierProduct>[2]) =>
      supplierApi.updateSupplierProduct(supplierId, productId, data),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: ['supplier', supplierId, 'products'] });
    },
  });
};

export const useRemoveProductFromSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, productId }: { supplierId: string; productId: string }) =>
      supplierApi.removeProductFromSupplier(supplierId, productId),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: ['supplier', supplierId, 'products'] });
    },
  });
};

export const useSearchSuppliers = (query: string) => {
  return useQuery({
    queryKey: ['suppliers', 'search', query],
    queryFn: () => supplierApi.searchSuppliers(query),
    enabled: query.length > 0,
  });
};
