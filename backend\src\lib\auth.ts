import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { UserRole } from '@prisma/client'

export const hashPassword = async (password: string): Promise<string> => {
return bcrypt.hash(password, 12)
}

export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
return bcrypt.compare(password, hashedPassword)
}

export const generateToken = (payload: { userId: string; email: string; role: UserRole }): string => {
const secret = process.env.JWT_SECRET
if (!secret) {
throw new Error('JWT_SECRET not configured')
}

return jwt.sign(payload, secret, {
expiresIn: process.env.JWT_EXPIRES_IN || '7d'
})
}