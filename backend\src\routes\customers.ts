import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { customerSchema } from '../lib/validation'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { auditLogger } from '../middleware/logging'
import { ChurnRisk, LoyaltyTier } from '@prisma/client'

export const customerRoutes = new Hono()

// Get all customers with filters and pagination
customerRoutes.get('/', authenticateToken, requireStaff, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const search = c.req.query('search') || ''
const loyaltyTier = c.req.query('loyaltyTier') as LoyaltyTier
const churnRisk = c.req.query('churnRisk') as ChurnRisk
const isActive = c.req.query('isActive')
const sortBy = c.req.query('sortBy') || 'createdAt'
const sortOrder = c.req.query('sortOrder') || 'desc'

const skip = (page - 1) * limit

const where = {
...(search && {
OR: [
{ firstName: { contains: search, mode: 'insensitive' as const } },
{ lastName: { contains: search, mode: 'insensitive' as const } },
{ email: { contains: search, mode: 'insensitive' as const } },
{ phone: { contains: search, mode: 'insensitive' as const } }
]
}),
...(loyaltyTier && { loyaltyTier }),
...(churnRisk && { churnRisk }),
...(isActive !== undefined && { isActive: isActive === 'true' })
}

const orderBy = { [sortBy]: sortOrder }

const [customers, total] = await Promise.all([
prisma.customer.findMany({
where,
include: {
sales: {
select: { total: true, createdAt: true },
orderBy: { createdAt: 'desc' },
take: 1
},
_count: {
select: { sales: true }
}
},
orderBy,
skip,
take: limit
}),
prisma.customer.count({ where })
])

return c.json({
customers,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Get single customer with full details
customerRoutes.get('/:id', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')

const customer = await prisma.customer.findUnique({
where: { id },
include: {
sales: {
include: {
items: {
include: {
product: {
select: { name: true, sku: true }
}
}
},
user: {
select: { firstName: true, lastName: true }
}
},
orderBy: { createdAt: 'desc' }
}
}
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

// Calculate additional metrics
const salesData = customer.sales
const totalOrders = salesData.length
const totalSpent = salesData.reduce((sum, sale) => sum + Number(sale.total), 0)
const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

const lastPurchase = salesData.length > 0 ? salesData[0].createdAt : null
const daysSinceLastPurchase = lastPurchase
? Math.floor((Date.now() - lastPurchase.getTime()) / (1000 * 60 * 60 * 24))
: null

return c.json({
customer: {
...customer,
metrics: {
totalOrders,
totalSpent,
averageOrderValue,
daysSinceLastPurchase
}
}
})
})

// Create customer
customerRoutes.post('/', authenticateToken, requireStaff, zValidator('json', customerSchema), auditLogger('CREATE', 'customer', ''), async (c) => {
const data = c.req.valid('json')

// Check if email already exists
if (data.email) {
const existingCustomer = await prisma.customer.findUnique({
where: { email: data.email }
})

    if (existingCustomer) {
      throw new HTTPException(409, { message: 'Customer with this email already exists' })
    }

}

const customer = await prisma.customer.create({
data
})

return c.json({
message: 'Customer created successfully',
customer
}, 201)
})

// Update customer
customerRoutes.put('/:id', authenticateToken, requireStaff, zValidator('json', customerSchema.partial()), auditLogger('UPDATE', 'customer', ''), async (c) => {
const id = c.req.param('id')
const data = c.req.valid('json')

const existingCustomer = await prisma.customer.findUnique({
where: { id }
})

if (!existingCustomer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

// Check if email is being changed and already exists
if (data.email && data.email !== existingCustomer.email) {
const emailExists = await prisma.customer.findUnique({
where: { email: data.email }
})

    if (emailExists) {
      throw new HTTPException(409, { message: 'Customer with this email already exists' })
    }

}

const customer = await prisma.customer.update({
where: { id },
data
})

return c.json({
message: 'Customer updated successfully',
customer
})
})

// Delete customer (soft delete)
customerRoutes.delete('/:id', authenticateToken, requireManager, auditLogger('DELETE', 'customer', ''), async (c) => {
const id = c.req.param('id')

const customer = await prisma.customer.findUnique({
where: { id }
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

await prisma.customer.update({
where: { id },
data: { isActive: false }
})

return c.json({ message: 'Customer deleted successfully' })
})

// Search customers by name, email, or phone
customerRoutes.get('/search/:query', authenticateToken, requireStaff, async (c) => {
const query = c.req.param('query')
const limit = parseInt(c.req.query('limit') || '10')

const customers = await prisma.customer.findMany({
where: {
isActive: true,
OR: [
{ firstName: { contains: query, mode: 'insensitive' } },
{ lastName: { contains: query, mode: 'insensitive' } },
{ email: { contains: query, mode: 'insensitive' } },
{ phone: { contains: query, mode: 'insensitive' } }
]
},
select: {
id: true,
firstName: true,
lastName: true,
email: true,
phone: true,
loyaltyPoints: true,
loyaltyTier: true
},
take: limit
})

return c.json({ customers })
})

// Update loyalty points
customerRoutes.post('/:id/loyalty', authenticateToken, requireStaff, zValidator('json', z.object({
points: z.number().int(),
reason: z.string().min(1, 'Reason is required')
})), async (c) => {
const id = c.req.param('id')
const { points, reason } = c.req.valid('json')

const customer = await prisma.customer.findUnique({
where: { id }
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

const newPoints = Math.max(0, customer.loyaltyPoints + points)

// Update loyalty tier based on points
let newTier = customer.loyaltyTier
if (newPoints >= 10000) newTier = LoyaltyTier.PLATINUM
else if (newPoints >= 5000) newTier = LoyaltyTier.GOLD
else if (newPoints >= 1000) newTier = LoyaltyTier.SILVER
else newTier = LoyaltyTier.BRONZE

const updatedCustomer = await prisma.customer.update({
where: { id },
data: {
loyaltyPoints: newPoints,
loyaltyTier: newTier
}
})

return c.json({
message: 'Loyalty points updated successfully',
customer: updatedCustomer
})
})

// Get customer analytics
customerRoutes.get('/:id/analytics', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')
const months = parseInt(c.req.query('months') || '12')

const customer = await prisma.customer.findUnique({
where: { id }
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

const startDate = new Date()
startDate.setMonth(startDate.getMonth() - months)

const [salesByMonth, topProducts, recentActivity] = await Promise.all([
// Sales by month
prisma.sale.groupBy({
by: ['createdAt'],
where: {
customerId: id,
createdAt: { gte: startDate }
},
_sum: { total: true },
_count: true
}),

    // Top purchased products
    prisma.saleItem.groupBy({
      by: ['productId'],
      where: {
        sale: {
          customerId: id,
          createdAt: { gte: startDate }
        }
      },
      _sum: { quantity: true, total: true },
      orderBy: { _sum: { total: 'desc' } },
      take: 10
    }),

    // Recent activity
    prisma.sale.findMany({
      where: { customerId: id },
      include: {
        items: {
          include: {
            product: {
              select: { name: true }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

])

// Get product details for top products
const productIds = topProducts.map(p => p.productId)
const products = await prisma.product.findMany({
where: { id: { in: productIds } },
select: { id: true, name: true, price: true }
})

const topProductsWithDetails = topProducts.map(tp => ({
...tp,
product: products.find(p => p.id === tp.productId)
}))

return c.json({
salesByMonth,
topProducts: topProductsWithDetails,
recentActivity
})
})

// Get at-risk customers (for AI dashboard)
customerRoutes.get('/analytics/at-risk', authenticateToken, requireStaff, async (c) => {
const limit = parseInt(c.req.query('limit') || '20')

const customers = await prisma.customer.findMany({
where: {
isActive: true,
churnRisk: { in: [ChurnRisk.HIGH, ChurnRisk.CRITICAL] }
},
include: {
sales: {
select: { createdAt: true, total: true },
orderBy: { createdAt: 'desc' },
take: 1
}
},
orderBy: [
{ churnRisk: 'desc' },
{ lastPurchaseAt: 'asc' }
],
take: limit
})

return c.json({ customers })
})