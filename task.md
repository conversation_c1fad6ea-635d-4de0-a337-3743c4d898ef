// package.json
{
"name": "ecommerce-pos-backend",
"version": "1.0.0",
"description": "E-commerce POS Backend with Hono, Prisma, PostgreSQL",
"main": "src/index.ts",
"scripts": {
"dev": "tsx watch src/index.ts",
"build": "tsc",
"start": "node dist/index.js",
"db:generate": "prisma generate",
"db:push": "prisma db push",
"db:migrate": "prisma migrate dev",
"db:studio": "prisma studio",
"db:seed": "tsx src/scripts/seed.ts"
},
"dependencies": {
"@hono/node-server": "^1.12.0",
"@prisma/client": "^5.15.0",
"hono": "^4.5.0",
"bcryptjs": "^2.4.3",
"jsonwebtoken": "^9.0.2",
"zod": "^3.23.8",
"@hono/zod-validator": "^0.2.2",
"cors": "^2.8.5",
"dotenv": "^16.4.5",
"openai": "^4.52.7",
"node-cron": "^3.0.3",
"multer": "^1.4.5-lts.1",
"@types/multer": "^1.4.11"
},
"devDependencies": {
"@types/bcryptjs": "^2.4.6",
"@types/jsonwebtoken": "^9.0.6",
"@types/node": "^20.14.10",
"@types/cors": "^2.8.17",
"@types/node-cron": "^3.0.11",
"prisma": "^5.15.0",
"tsx": "^4.16.2",
"typescript": "^5.5.3"
}
}

// .env
DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce_pos?schema=public"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
OPENAI_API_KEY="your-openai-api-key"
PORT=3001
NODE_ENV="development"

// .env.example
DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce_pos?schema=public"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
OPENAI_API_KEY="your-openai-api-key"
PORT=3001
NODE_ENV="development"
==========
// prisma/schema.prisma
generator client {
provider = "prisma-client-js"
}

datasource db {
provider = "postgresql"
url = env("DATABASE_URL")
}

model User {
id String @id @default(cuid())
email String @unique
password String
firstName String
lastName String
role UserRole @default(STAFF)
isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
sales Sale[]
auditLogs AuditLog[]
createdProducts Product[] @relation("CreatedBy")

@@map("users")
}

model Customer {
id String @id @default(cuid())
email String? @unique
phone String?
firstName String
lastName String
dateOfBirth DateTime?
address String?
city String?
state String?
zipCode String?
country String?

// Loyalty & Analytics
loyaltyPoints Int @default(0)
loyaltyTier LoyaltyTier @default(BRONZE)
totalSpent Decimal @default(0) @db.Decimal(10,2)
totalOrders Int @default(0)
lastPurchaseAt DateTime?
averageOrderValue Decimal @default(0) @db.Decimal(10,2)

// AI Insights
lifetimeValue Decimal? @db.Decimal(10,2)
churnRisk ChurnRisk @default(LOW)
segment String?

isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
sales Sale[]

@@map("customers")
}

model Category {
id String @id @default(cuid())
name String
description String?
slug String @unique
imageUrl String?
isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
products Product[]

@@map("categories")
}

model Product {
id String @id @default(cuid())
name String
description String?
sku String @unique
barcode String? @unique
price Decimal @db.Decimal(10,2)
cost Decimal @db.Decimal(10,2)
markup Decimal @db.Decimal(5,2) @default(0)

// Inventory
stockQuantity Int @default(0)
minStockLevel Int @default(0)
maxStockLevel Int?
reorderPoint Int @default(0)

// Product Info
weight Decimal? @db.Decimal(8,2)
dimensions String?
imageUrl String?
tags String[]

// AI Features
aiGenerated Boolean @default(false)
salesForecast Json?
performanceScore Decimal? @db.Decimal(3,2)

// Status
isActive Boolean @default(true)
isFeatured Boolean @default(false)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
createdById String?

// Relations
categoryId String?
category Category? @relation(fields: [categoryId], references: [id])
createdBy User? @relation("CreatedBy", fields: [createdById], references: [id])
saleItems SaleItem[]
inventoryLogs InventoryLog[]
suppliers ProductSupplier[]

@@map("products")
}

model Supplier {
id String @id @default(cuid())
name String
email String?
phone String?
address String?
city String?
state String?
zipCode String?
country String?

contactPerson String?
website String?
notes String?

isActive Boolean @default(true)
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
products ProductSupplier[]
purchaseOrders PurchaseOrder[]

@@map("suppliers")
}

model ProductSupplier {
id String @id @default(cuid())
productId String
supplierId String
supplierSku String?
leadTime Int? // days
minOrderQty Int @default(1)
unitCost Decimal @db.Decimal(10,2)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
supplier Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)

@@unique([productId, supplierId])
@@map("product_suppliers")
}

model PurchaseOrder {
id String @id @default(cuid())
orderNumber String @unique
supplierId String
status PurchaseOrderStatus @default(DRAFT)

orderDate DateTime @default(now())
expectedDate DateTime?
receivedDate DateTime?

subtotal Decimal @db.Decimal(10,2)
tax Decimal @db.Decimal(10,2) @default(0)
total Decimal @db.Decimal(10,2)

notes String?

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
supplier Supplier @relation(fields: [supplierId], references: [id])
items PurchaseOrderItem[]

@@map("purchase_orders")
}

model PurchaseOrderItem {
id String @id @default(cuid())
purchaseOrderId String
productId String

quantity Int
unitCost Decimal @db.Decimal(10,2)
totalCost Decimal @db.Decimal(10,2)

receivedQty Int @default(0)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
purchaseOrder PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
product Product @relation(fields: [productId], references: [id])

@@map("purchase_order_items")
}

model Sale {
id String @id @default(cuid())
saleNumber String @unique
customerId String?
userId String

// Sale Details
subtotal Decimal @db.Decimal(10,2)
tax Decimal @db.Decimal(10,2) @default(0)
discount Decimal @db.Decimal(10,2) @default(0)
total Decimal @db.Decimal(10,2)

// Payment
paymentMethod PaymentMethod
paymentStatus PaymentStatus @default(COMPLETED)

// Loyalty
pointsEarned Int @default(0)
pointsUsed Int @default(0)

notes String?

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

// Relations
customer Customer? @relation(fields: [customerId], references: [id])
user User @relation(fields: [userId], references: [id])
items SaleItem[]

@@map("sales")
}

model SaleItem {
id String @id @default(cuid())
saleId String
productId String

quantity Int
unitPrice Decimal @db.Decimal(10,2)
discount Decimal @db.Decimal(10,2) @default(0)
total Decimal @db.Decimal(10,2)

createdAt DateTime @default(now())

// Relations
sale Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)
product Product @relation(fields: [productId], references: [id])

@@map("sale_items")
}

model InventoryLog {
id String @id @default(cuid())
productId String
type InventoryLogType
quantity Int
previousQty Int
newQty Int

reason String?
reference String? // PO number, sale number, etc.

createdAt DateTime @default(now())

// Relations
product Product @relation(fields: [productId], references: [id])

@@map("inventory_logs")
}

model AuditLog {
id String @id @default(cuid())
userId String?
action String
entity String // table name
entityId String
oldValues Json?
newValues Json?
ipAddress String?
userAgent String?

createdAt DateTime @default(now())

// Relations
user User? @relation(fields: [userId], references: [id])

@@map("audit_logs")
}

model Setting {
id String @id @default(cuid())
key String @unique
value String
type SettingType @default(STRING)

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

@@map("settings")
}

// Enums
enum UserRole {
ADMIN
MANAGER
STAFF
}

enum LoyaltyTier {
BRONZE
SILVER
GOLD
PLATINUM
}

enum ChurnRisk {
LOW
MEDIUM
HIGH
CRITICAL
}

enum PaymentMethod {
CASH
CREDIT_CARD
DEBIT_CARD
PAYPAL
STRIPE
LOYALTY_POINTS
}

enum PaymentStatus {
PENDING
COMPLETED
FAILED
REFUNDED
}

enum PurchaseOrderStatus {
DRAFT
SENT
CONFIRMED
RECEIVED
CANCELLED
}

enum InventoryLogType {
PURCHASE
SALE
ADJUSTMENT
RETURN
DAMAGED
EXPIRED
}

enum SettingType {
STRING
NUMBER
BOOLEAN
JSON
}
======
// src/index.ts
import { serve } from '@hono/node-server'
import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { HTTPException } from 'hono/http-exception'
import dotenv from 'dotenv'

import { authRoutes } from './routes/auth'
import { userRoutes } from './routes/users'
import { productRoutes } from './routes/products'
import { customerRoutes } from './routes/customers'
import { saleRoutes } from './routes/sales'
import { inventoryRoutes } from './routes/inventory'
import { supplierRoutes } from './routes/suppliers'
import { purchaseOrderRoutes } from './routes/purchase-orders'
import { dashboardRoutes } from './routes/dashboard'
import { reportRoutes } from './routes/reports'
import { settingRoutes } from './routes/settings'
import { auditRoutes } from './routes/audit'
import { aiRoutes } from './routes/ai'

import { errorHandler } from './middleware/error'
import { requestLogger } from './middleware/logging'

dotenv.config()

const app = new Hono()

// Middleware
app.use('_', logger())
app.use('_', prettyJSON())
app.use('_', cors({
origin: process.env.NODE_ENV === 'production'
? ['https://yourfrontend.com']
: ['http://localhost:3000', 'http://localhost:3001'],
credentials: true,
}))
app.use('_', requestLogger)

// Health check
app.get('/', (c) => {
return c.json({
message: 'E-commerce POS API',
version: '1.0.0',
status: 'healthy',
timestamp: new Date().toISOString()
})
})

app.get('/health', (c) => {
return c.json({ status: 'OK', timestamp: new Date().toISOString() })
})

// Routes
app.route('/api/auth', authRoutes)
app.route('/api/users', userRoutes)
app.route('/api/products', productRoutes)
app.route('/api/customers', customerRoutes)
app.route('/api/sales', saleRoutes)
app.route('/api/inventory', inventoryRoutes)
app.route('/api/suppliers', supplierRoutes)
app.route('/api/purchase-orders', purchaseOrderRoutes)
app.route('/api/dashboard', dashboardRoutes)
app.route('/api/reports', reportRoutes)
app.route('/api/settings', settingRoutes)
app.route('/api/audit', auditRoutes)
app.route('/api/ai', aiRoutes)

// Global error handler
app.onError(errorHandler)

// 404 handler
app.notFound((c) => {
return c.json({ error: 'Not Found', path: c.req.path }, 404)
})

const port = parseInt(process.env.PORT || '3001')

console.log(`🚀 Server starting on port ${port}`)
console.log(`🌍 Environment: ${process.env.NODE_ENV}`)

serve({
fetch: app.fetch,
port,
})

# export default app

// src/middleware/auth.ts
import { Context, Next } from 'hono'
import { HTTPException } from 'hono/http-exception'
import jwt from 'jsonwebtoken'
import { prisma } from '../lib/db'
import { UserRole } from '@prisma/client'

interface JWTPayload {
userId: string
email: string
role: UserRole
}

declare module 'hono' {
interface ContextVariableMap {
user: {
id: string
email: string
role: UserRole
firstName: string
lastName: string
}
}
}

export const authenticateToken = async (c: Context, next: Next) => {
try {
const authHeader = c.req.header('Authorization')
const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      throw new HTTPException(401, { message: 'Access token required' })
    }

    const secret = process.env.JWT_SECRET
    if (!secret) {
      throw new HTTPException(500, { message: 'JWT secret not configured' })
    }

    const decoded = jwt.verify(token, secret) as JWTPayload

    // Get fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!user) {
      throw new HTTPException(401, { message: 'User not found' })
    }

    if (!user.isActive) {
      throw new HTTPException(401, { message: 'Account is disabled' })
    }

    c.set('user', user)
    await next()

} catch (error) {
if (error instanceof jwt.JsonWebTokenError) {
throw new HTTPException(401, { message: 'Invalid token' })
}
if (error instanceof jwt.TokenExpiredError) {
throw new HTTPException(401, { message: 'Token expired' })
}
throw error
}
}

export const requireRole = (roles: UserRole[]) => {
return async (c: Context, next: Next) => {
const user = c.get('user')

    if (!user) {
      throw new HTTPException(401, { message: 'Authentication required' })
    }

    if (!roles.includes(user.role)) {
      throw new HTTPException(403, { message: 'Insufficient permissions' })
    }

    await next()

}
}

export const requireAdmin = requireRole([UserRole.ADMIN])
export const requireManager = requireRole([UserRole.ADMIN, UserRole.MANAGER])
export const requireStaff = requireRole([UserRole.ADMIN, UserRole.MANAGER, UserRole.STAFF])

// src/middleware/error.ts
import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { ZodError } from 'zod'

export const errorHandler = (err: Error, c: Context) => {
console.error('Error:', err)

if (err instanceof HTTPException) {
return c.json({ error: err.message }, err.status)
}

if (err instanceof ZodError) {
return c.json({
error: 'Validation error',
details: err.errors.map(e => ({
path: e.path.join('.'),
message: e.message
}))
}, 400)
}

// Prisma errors
if (err.message.includes('Unique constraint failed')) {
return c.json({ error: 'Record already exists' }, 409)
}

if (err.message.includes('Record to update not found')) {
return c.json({ error: 'Record not found' }, 404)
}

// Default error
return c.json({
error: 'Internal server error',
...(process.env.NODE_ENV === 'development' && { details: err.message })
}, 500)
}

// src/middleware/logging.ts
import { Context, Next } from 'hono'
import { prisma } from '../lib/db'

export const requestLogger = async (c: Context, next: Next) => {
const start = Date.now()
await next()
const ms = Date.now() - start

console.log(`${c.req.method} ${c.req.path} - ${c.res.status} (${ms}ms)`)
}

export const auditLogger = (action: string, entity: string, entityId: string) => {
return async (c: Context, next: Next) => {
await next()

    // Only log successful operations
    if (c.res.status < 400) {
      const user = c.get('user')
      const userAgent = c.req.header('User-Agent')
      const forwarded = c.req.header('X-Forwarded-For')
      const realIp = c.req.header('X-Real-IP')
      const ipAddress = forwarded?.split(',')[0] || realIp || 'unknown'

      try {
        await prisma.auditLog.create({
          data: {
            userId: user?.id,
            action,
            entity,
            entityId,
            ipAddress,
            userAgent: userAgent || 'unknown'
          }
        })
      } catch (error) {
        console.error('Failed to create audit log:', error)
      }
    }

}
}
===================
// src/lib/db.ts
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
prisma: PrismaClient | undefined
}

export const prisma =
globalForPrisma.prisma ??
new PrismaClient({
log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// src/lib/auth.ts
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { UserRole } from '@prisma/client'

export const hashPassword = async (password: string): Promise<string> => {
return bcrypt.hash(password, 12)
}

export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
return bcrypt.compare(password, hashedPassword)
}

export const generateToken = (payload: { userId: string; email: string; role: UserRole }): string => {
const secret = process.env.JWT_SECRET
if (!secret) {
throw new Error('JWT_SECRET not configured')
}

return jwt.sign(payload, secret, {
expiresIn: process.env.JWT_EXPIRES_IN || '7d'
})
}

// src/lib/validation.ts
import { z } from 'zod'
import { UserRole, PaymentMethod, PaymentStatus, LoyaltyTier, ChurnRisk } from '@prisma/client'

export const loginSchema = z.object({
email: z.string().email('Invalid email format'),
password: z.string().min(6, 'Password must be at least 6 characters')
})

export const registerSchema = z.object({
email: z.string().email('Invalid email format'),
password: z.string().min(6, 'Password must be at least 6 characters'),
firstName: z.string().min(1, 'First name is required'),
lastName: z.string().min(1, 'Last name is required'),
role: z.nativeEnum(UserRole).optional().default(UserRole.STAFF)
})

export const customerSchema = z.object({
firstName: z.string().min(1, 'First name is required'),
lastName: z.string().min(1, 'Last name is required'),
email: z.string().email('Invalid email format').optional(),
phone: z.string().optional(),
dateOfBirth: z.string().transform(str => str ? new Date(str) : undefined).optional(),
address: z.string().optional(),
city: z.string().optional(),
state: z.string().optional(),
zipCode: z.string().optional(),
country: z.string().optional(),
loyaltyTier: z.nativeEnum(LoyaltyTier).optional(),
churnRisk: z.nativeEnum(ChurnRisk).optional()
})

export const productSchema = z.object({
name: z.string().min(1, 'Product name is required'),
description: z.string().optional(),
sku: z.string().min(1, 'SKU is required'),
barcode: z.string().optional(),
price: z.number().positive('Price must be positive'),
cost: z.number().positive('Cost must be positive'),
stockQuantity: z.number().int().min(0, 'Stock cannot be negative'),
minStockLevel: z.number().int().min(0, 'Min stock level cannot be negative'),
maxStockLevel: z.number().int().optional(),
reorderPoint: z.number().int().min(0, 'Reorder point cannot be negative'),
weight: z.number().positive().optional(),
dimensions: z.string().optional(),
imageUrl: z.string().url().optional(),
tags: z.array(z.string()).optional(),
categoryId: z.string().optional(),
isFeatured: z.boolean().optional()
})

export const saleSchema = z.object({
customerId: z.string().optional(),
items: z.array(z.object({
productId: z.string(),
quantity: z.number().int().positive('Quantity must be positive'),
unitPrice: z.number().positive('Unit price must be positive'),
discount: z.number().min(0, 'Discount cannot be negative').optional()
})),
paymentMethod: z.nativeEnum(PaymentMethod),
tax: z.number().min(0, 'Tax cannot be negative').optional(),
discount: z.number().min(0, 'Discount cannot be negative').optional(),
pointsUsed: z.number().int().min(0, 'Points used cannot be negative').optional(),
notes: z.string().optional()
})

// src/routes/auth.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { prisma } from '../lib/db'
import { hashPassword, verifyPassword, generateToken } from '../lib/auth'
import { loginSchema, registerSchema } from '../lib/validation'
import { authenticateToken, requireAdmin } from '../middleware/auth'

export const authRoutes = new Hono()

// Login
authRoutes.post('/login', zValidator('json', loginSchema), async (c) => {
const { email, password } = c.req.valid('json')

const user = await prisma.user.findUnique({
where: { email },
select: {
id: true,
email: true,
password: true,
firstName: true,
lastName: true,
role: true,
isActive: true
}
})

if (!user || !user.isActive) {
throw new HTTPException(401, { message: 'Invalid credentials' })
}

const isValidPassword = await verifyPassword(password, user.password)
if (!isValidPassword) {
throw new HTTPException(401, { message: 'Invalid credentials' })
}

const token = generateToken({
userId: user.id,
email: user.email,
role: user.role
})

// Remove password from response
const { password: \_, ...userWithoutPassword } = user

return c.json({
message: 'Login successful',
token,
user: userWithoutPassword
})
})

// Register (Admin only)
authRoutes.post('/register', authenticateToken, requireAdmin, zValidator('json', registerSchema), async (c) => {
const { email, password, firstName, lastName, role } = c.req.valid('json')

const existingUser = await prisma.user.findUnique({
where: { email }
})

if (existingUser) {
throw new HTTPException(409, { message: 'User already exists' })
}

const hashedPassword = await hashPassword(password)

const user = await prisma.user.create({
data: {
email,
password: hashedPassword,
firstName,
lastName,
role
},
select: {
id: true,
email: true,
firstName: true,
lastName: true,
role: true,
isActive: true,
createdAt: true
}
})

return c.json({
message: 'User created successfully',
user
}, 201)
})

// Get current user profile
authRoutes.get('/me', authenticateToken, async (c) => {
const user = c.get('user')

const userDetails = await prisma.user.findUnique({
where: { id: user.id },
select: {
id: true,
email: true,
firstName: true,
lastName: true,
role: true,
isActive: true,
createdAt: true,
updatedAt: true
}
})

return c.json({ user: userDetails })
})

// Refresh token
authRoutes.post('/refresh', authenticateToken, async (c) => {
const user = c.get('user')

const token = generateToken({
userId: user.id,
email: user.email,
role: user.role
})

return c.json({
message: 'Token refreshed successfully',
token
})
})

// Change password
authRoutes.post('/change-password', authenticateToken, zValidator('json', z.object({
currentPassword: z.string().min(1, 'Current password is required'),
newPassword: z.string().min(6, 'New password must be at least 6 characters')
})), async (c) => {
const user = c.get('user')
const { currentPassword, newPassword } = c.req.valid('json')

const dbUser = await prisma.user.findUnique({
where: { id: user.id }
})

if (!dbUser) {
throw new HTTPException(404, { message: 'User not found' })
}

const isValidPassword = await verifyPassword(currentPassword, dbUser.password)
if (!isValidPassword) {
throw new HTTPException(400, { message: 'Current password is incorrect' })
}

const hashedNewPassword = await hashPassword(newPassword)

await prisma.user.update({
where: { id: user.id },
data: { password: hashedNewPassword }
})

return c.json({ message: 'Password changed successfully' })
})
===========
// src/routes/products.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { productSchema } from '../lib/validation'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { auditLogger } from '../middleware/logging'
import { InventoryLogType } from '@prisma/client'

export const productRoutes = new Hono()

// Get all products with filters and pagination
productRoutes.get('/', authenticateToken, requireStaff, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const search = c.req.query('search') || ''
const categoryId = c.req.query('categoryId')
const isActive = c.req.query('isActive')
const lowStock = c.req.query('lowStock') === 'true'

const skip = (page - 1) \* limit

const where = {
...(search && {
OR: [
{ name: { contains: search, mode: 'insensitive' as const } },
{ sku: { contains: search, mode: 'insensitive' as const } },
{ barcode: { contains: search, mode: 'insensitive' as const } },
{ description: { contains: search, mode: 'insensitive' as const } }
]
}),
...(categoryId && { categoryId }),
...(isActive !== undefined && { isActive: isActive === 'true' }),
...(lowStock && {
stockQuantity: { lte: prisma.raw('min_stock_level') }
})
}

const [products, total] = await Promise.all([
prisma.product.findMany({
where,
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
},
_count: {
select: { saleItems: true }
}
},
orderBy: { createdAt: 'desc' },
skip,
take: limit
}),
prisma.product.count({ where })
])

return c.json({
products,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Get single product
productRoutes.get('/:id', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')

const product = await prisma.product.findUnique({
where: { id },
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
},
suppliers: {
include: { supplier: true }
},
inventoryLogs: {
orderBy: { createdAt: 'desc' },
take: 10
},
saleItems: {
include: { sale: true },
orderBy: { createdAt: 'desc' },
take: 10
}
}
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

return c.json({ product })
})

// Create product
productRoutes.post('/', authenticateToken, requireManager, zValidator('json', productSchema), auditLogger('CREATE', 'product', ''), async (c) => {
const data = c.req.valid('json')
const user = c.get('user')

// Calculate markup percentage
const markup = ((data.price - data.cost) / data.cost) \* 100

const product = await prisma.product.create({
data: {
...data,
markup,
createdById: user.id
},
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
}
}
})

// Create initial inventory log
if (data.stockQuantity > 0) {
await prisma.inventoryLog.create({
data: {
productId: product.id,
type: InventoryLogType.ADJUSTMENT,
quantity: data.stockQuantity,
previousQty: 0,
newQty: data.stockQuantity,
reason: 'Initial stock'
}
})
}

return c.json({
message: 'Product created successfully',
product
}, 201)
})

// Update product
productRoutes.put('/:id', authenticateToken, requireManager, zValidator('json', productSchema.partial()), auditLogger('UPDATE', 'product', ''), async (c) => {
const id = c.req.param('id')
const data = c.req.valid('json')

const existingProduct = await prisma.product.findUnique({
where: { id }
})

if (!existingProduct) {
throw new HTTPException(404, { message: 'Product not found' })
}

// Calculate markup if price or cost changed
let markup = existingProduct.markup
if (data.price || data.cost) {
const newPrice = data.price || existingProduct.price
const newCost = data.cost || existingProduct.cost
markup = ((Number(newPrice) - Number(newCost)) / Number(newCost)) \* 100
}

const product = await prisma.product.update({
where: { id },
data: {
...data,
markup
},
include: {
category: true,
createdBy: {
select: { firstName: true, lastName: true }
}
}
})

// Create inventory log if stock quantity changed
if (data.stockQuantity !== undefined && data.stockQuantity !== existingProduct.stockQuantity) {
await prisma.inventoryLog.create({
data: {
productId: id,
type: InventoryLogType.ADJUSTMENT,
quantity: data.stockQuantity - existingProduct.stockQuantity,
previousQty: existingProduct.stockQuantity,
newQty: data.stockQuantity,
reason: 'Manual adjustment'
}
})
}

return c.json({
message: 'Product updated successfully',
product
})
})

// Delete product (soft delete)
productRoutes.delete('/:id', authenticateToken, requireManager, auditLogger('DELETE', 'product', ''), async (c) => {
const id = c.req.param('id')

const product = await prisma.product.findUnique({
where: { id }
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

await prisma.product.update({
where: { id },
data: { isActive: false }
})

return c.json({ message: 'Product deleted successfully' })
})

// Bulk update products
productRoutes.post('/bulk-update', authenticateToken, requireManager, zValidator('json', z.object({
productIds: z.array(z.string()),
updates: z.object({
categoryId: z.string().optional(),
isActive: z.boolean().optional(),
isFeatured: z.boolean().optional(),
tags: z.array(z.string()).optional()
})
})), async (c) => {
const { productIds, updates } = c.req.valid('json')

await prisma.product.updateMany({
where: { id: { in: productIds } },
data: updates
})

return c.json({
message: `${productIds.length} products updated successfully`
})
})

// Get products by barcode
productRoutes.get('/barcode/:barcode', authenticateToken, requireStaff, async (c) => {
const barcode = c.req.param('barcode')

const product = await prisma.product.findUnique({
where: { barcode },
include: {
category: true
}
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

return c.json({ product })
})

// Get low stock products
productRoutes.get('/alerts/low-stock', authenticateToken, requireStaff, async (c) => {
const products = await prisma.product.findMany({
where: {
isActive: true,
stockQuantity: { lte: prisma.raw('min_stock_level') }
},
include: {
category: true
},
orderBy: [
{ stockQuantity: 'asc' },
{ name: 'asc' }
]
})

return c.json({ products })
})

// Adjust stock
productRoutes.post('/:id/adjust-stock', authenticateToken, requireManager, zValidator('json', z.object({
quantity: z.number().int(),
reason: z.string().min(1, 'Reason is required'),
type: z.nativeEnum(InventoryLogType).optional().default(InventoryLogType.ADJUSTMENT)
})), async (c) => {
const id = c.req.param('id')
const { quantity, reason, type } = c.req.valid('json')

const product = await prisma.product.findUnique({
where: { id }
})

if (!product) {
throw new HTTPException(404, { message: 'Product not found' })
}

const newQuantity = Math.max(0, product.stockQuantity + quantity)

await prisma.$transaction(async (tx) => {
await tx.product.update({
where: { id },
data: { stockQuantity: newQuantity }
})

    await tx.inventoryLog.create({
      data: {
        productId: id,
        type,
        quantity,
        previousQty: product.stockQuantity,
        newQty: newQuantity,
        reason
      }
    })

})

return c.json({
message: 'Stock adjusted successfully',
newQuantity
})
})
============
// src/routes/customers.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { customerSchema } from '../lib/validation'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { auditLogger } from '../middleware/logging'
import { ChurnRisk, LoyaltyTier } from '@prisma/client'

export const customerRoutes = new Hono()

// Get all customers with filters and pagination
customerRoutes.get('/', authenticateToken, requireStaff, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const search = c.req.query('search') || ''
const loyaltyTier = c.req.query('loyaltyTier') as LoyaltyTier
const churnRisk = c.req.query('churnRisk') as ChurnRisk
const isActive = c.req.query('isActive')
const sortBy = c.req.query('sortBy') || 'createdAt'
const sortOrder = c.req.query('sortOrder') || 'desc'

const skip = (page - 1) \* limit

const where = {
...(search && {
OR: [
{ firstName: { contains: search, mode: 'insensitive' as const } },
{ lastName: { contains: search, mode: 'insensitive' as const } },
{ email: { contains: search, mode: 'insensitive' as const } },
{ phone: { contains: search, mode: 'insensitive' as const } }
]
}),
...(loyaltyTier && { loyaltyTier }),
...(churnRisk && { churnRisk }),
...(isActive !== undefined && { isActive: isActive === 'true' })
}

const orderBy = { [sortBy]: sortOrder }

const [customers, total] = await Promise.all([
prisma.customer.findMany({
where,
include: {
sales: {
select: { total: true, createdAt: true },
orderBy: { createdAt: 'desc' },
take: 1
},
_count: {
select: { sales: true }
}
},
orderBy,
skip,
take: limit
}),
prisma.customer.count({ where })
])

return c.json({
customers,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Get single customer with full details
customerRoutes.get('/:id', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')

const customer = await prisma.customer.findUnique({
where: { id },
include: {
sales: {
include: {
items: {
include: {
product: {
select: { name: true, sku: true }
}
}
},
user: {
select: { firstName: true, lastName: true }
}
},
orderBy: { createdAt: 'desc' }
}
}
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

// Calculate additional metrics
const salesData = customer.sales
const totalOrders = salesData.length
const totalSpent = salesData.reduce((sum, sale) => sum + Number(sale.total), 0)
const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

const lastPurchase = salesData.length > 0 ? salesData[0].createdAt : null
const daysSinceLastPurchase = lastPurchase
? Math.floor((Date.now() - lastPurchase.getTime()) / (1000 _ 60 _ 60 \* 24))
: null

return c.json({
customer: {
...customer,
metrics: {
totalOrders,
totalSpent,
averageOrderValue,
daysSinceLastPurchase
}
}
})
})

// Create customer
customerRoutes.post('/', authenticateToken, requireStaff, zValidator('json', customerSchema), auditLogger('CREATE', 'customer', ''), async (c) => {
const data = c.req.valid('json')

// Check if email already exists
if (data.email) {
const existingCustomer = await prisma.customer.findUnique({
where: { email: data.email }
})

    if (existingCustomer) {
      throw new HTTPException(409, { message: 'Customer with this email already exists' })
    }

}

const customer = await prisma.customer.create({
data
})

return c.json({
message: 'Customer created successfully',
customer
}, 201)
})

// Update customer
customerRoutes.put('/:id', authenticateToken, requireStaff, zValidator('json', customerSchema.partial()), auditLogger('UPDATE', 'customer', ''), async (c) => {
const id = c.req.param('id')
const data = c.req.valid('json')

const existingCustomer = await prisma.customer.findUnique({
where: { id }
})

if (!existingCustomer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

// Check if email is being changed and already exists
if (data.email && data.email !== existingCustomer.email) {
const emailExists = await prisma.customer.findUnique({
where: { email: data.email }
})

    if (emailExists) {
      throw new HTTPException(409, { message: 'Customer with this email already exists' })
    }

}

const customer = await prisma.customer.update({
where: { id },
data
})

return c.json({
message: 'Customer updated successfully',
customer
})
})

// Delete customer (soft delete)
customerRoutes.delete('/:id', authenticateToken, requireManager, auditLogger('DELETE', 'customer', ''), async (c) => {
const id = c.req.param('id')

const customer = await prisma.customer.findUnique({
where: { id }
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

await prisma.customer.update({
where: { id },
data: { isActive: false }
})

return c.json({ message: 'Customer deleted successfully' })
})

// Search customers by name, email, or phone
customerRoutes.get('/search/:query', authenticateToken, requireStaff, async (c) => {
const query = c.req.param('query')
const limit = parseInt(c.req.query('limit') || '10')

const customers = await prisma.customer.findMany({
where: {
isActive: true,
OR: [
{ firstName: { contains: query, mode: 'insensitive' } },
{ lastName: { contains: query, mode: 'insensitive' } },
{ email: { contains: query, mode: 'insensitive' } },
{ phone: { contains: query, mode: 'insensitive' } }
]
},
select: {
id: true,
firstName: true,
lastName: true,
email: true,
phone: true,
loyaltyPoints: true,
loyaltyTier: true
},
take: limit
})

return c.json({ customers })
})

// Update loyalty points
customerRoutes.post('/:id/loyalty', authenticateToken, requireStaff, zValidator('json', z.object({
points: z.number().int(),
reason: z.string().min(1, 'Reason is required')
})), async (c) => {
const id = c.req.param('id')
const { points, reason } = c.req.valid('json')

const customer = await prisma.customer.findUnique({
where: { id }
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

const newPoints = Math.max(0, customer.loyaltyPoints + points)

// Update loyalty tier based on points
let newTier = customer.loyaltyTier
if (newPoints >= 10000) newTier = LoyaltyTier.PLATINUM
else if (newPoints >= 5000) newTier = LoyaltyTier.GOLD
else if (newPoints >= 1000) newTier = LoyaltyTier.SILVER
else newTier = LoyaltyTier.BRONZE

const updatedCustomer = await prisma.customer.update({
where: { id },
data: {
loyaltyPoints: newPoints,
loyaltyTier: newTier
}
})

return c.json({
message: 'Loyalty points updated successfully',
customer: updatedCustomer
})
})

// Get customer analytics
customerRoutes.get('/:id/analytics', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')
const months = parseInt(c.req.query('months') || '12')

const customer = await prisma.customer.findUnique({
where: { id }
})

if (!customer) {
throw new HTTPException(404, { message: 'Customer not found' })
}

const startDate = new Date()
startDate.setMonth(startDate.getMonth() - months)

const [salesByMonth, topProducts, recentActivity] = await Promise.all([
// Sales by month
prisma.sale.groupBy({
by: ['createdAt'],
where: {
customerId: id,
createdAt: { gte: startDate }
},
\_sum: { total: true },
\_count: true
}),

    // Top purchased products
    prisma.saleItem.groupBy({
      by: ['productId'],
      where: {
        sale: {
          customerId: id,
          createdAt: { gte: startDate }
        }
      },
      _sum: { quantity: true, total: true },
      orderBy: { _sum: { total: 'desc' } },
      take: 10
    }),

    // Recent activity
    prisma.sale.findMany({
      where: { customerId: id },
      include: {
        items: {
          include: {
            product: {
              select: { name: true }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

])

// Get product details for top products
const productIds = topProducts.map(p => p.productId)
const products = await prisma.product.findMany({
where: { id: { in: productIds } },
select: { id: true, name: true, price: true }
})

const topProductsWithDetails = topProducts.map(tp => ({
...tp,
product: products.find(p => p.id === tp.productId)
}))

return c.json({
salesByMonth,
topProducts: topProductsWithDetails,
recentActivity
})
})

// Get at-risk customers (for AI dashboard)
customerRoutes.get('/analytics/at-risk', authenticateToken, requireStaff, async (c) => {
const limit = parseInt(c.req.query('limit') || '20')

const customers = await prisma.customer.findMany({
where: {
isActive: true,
churnRisk: { in: [ChurnRisk.HIGH, ChurnRisk.CRITICAL] }
},
include: {
sales: {
select: { createdAt: true, total: true },
orderBy: { createdAt: 'desc' },
take: 1
}
},
orderBy: [
{ churnRisk: 'desc' },
{ lastPurchaseAt: 'asc' }
],
take: limit
})

return c.json({ customers })
})
==========

// src/routes/sales.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { saleSchema } from '../lib/validation'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { auditLogger } from '../middleware/logging'
import { InventoryLogType, PaymentStatus, LoyaltyTier } from '@prisma/client'

export const saleRoutes = new Hono()

// Get all sales with filters and pagination
saleRoutes.get('/', authenticateToken, requireStaff, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const search = c.req.query('search') || ''
const startDate = c.req.query('startDate')
const endDate = c.req.query('endDate')
const paymentMethod = c.req.query('paymentMethod')
const userId = c.req.query('userId')
const customerId = c.req.query('customerId')

const skip = (page - 1) \* limit

const where = {
...(search && {
OR: [
{ saleNumber: { contains: search, mode: 'insensitive' as const } },
{ customer: {
OR: [
{ firstName: { contains: search, mode: 'insensitive' as const } },
{ lastName: { contains: search, mode: 'insensitive' as const } }
]
}}
]
}),
...(startDate && endDate && {
createdAt: {
gte: new Date(startDate),
lte: new Date(endDate)
}
}),
...(paymentMethod && { paymentMethod }),
...(userId && { userId }),
...(customerId && { customerId })
}

const [sales, total] = await Promise.all([
prisma.sale.findMany({
where,
include: {
customer: {
select: {
id: true,
firstName: true,
lastName: true,
email: true
}
},
user: {
select: {
firstName: true,
lastName: true
}
},
items: {
include: {
product: {
select: {
name: true,
sku: true
}
}
}
}
},
orderBy: { createdAt: 'desc' },
skip,
take: limit
}),
prisma.sale.count({ where })
])

return c.json({
sales,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Get single sale
saleRoutes.get('/:id', authenticateToken, requireStaff, async (c) => {
const id = c.req.param('id')

const sale = await prisma.sale.findUnique({
where: { id },
include: {
customer: true,
user: {
select: {
firstName: true,
lastName: true
}
},
items: {
include: {
product: true
}
}
}
})

if (!sale) {
throw new HTTPException(404, { message: 'Sale not found' })
}

return c.json({ sale })
})

// Create sale (POS transaction)
saleRoutes.post('/', authenticateToken, requireStaff, zValidator('json', saleSchema), auditLogger('CREATE', 'sale', ''), async (c) => {
const data = c.req.valid('json')
const user = c.get('user')

// Generate sale number
const saleCount = await prisma.sale.count()
const saleNumber = `SALE-${Date.now()}-${String(saleCount + 1).padStart(4, '0')}`

// Validate products and check stock
const productIds = data.items.map(item => item.productId)
const products = await prisma.product.findMany({
where: { id: { in: productIds }, isActive: true }
})

if (products.length !== productIds.length) {
throw new HTTPException(400, { message: 'Some products not found or inactive' })
}

// Check stock availability
for (const item of data.items) {
const product = products.find(p => p.id === item.productId)
if (!product) continue

    if (product.stockQuantity < item.quantity) {
      throw new HTTPException(400, {
        message: `Insufficient stock for ${product.name}. Available: ${product.stockQuantity}, Required: ${item.quantity}`
      })
    }

}

// Calculate totals
let subtotal = 0
const saleItems = data.items.map(item => {
const product = products.find(p => p.id === item.productId)!
const itemDiscount = item.discount || 0
const total = (item.unitPrice \* item.quantity) - itemDiscount
subtotal += total

    return {
      productId: item.productId,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      discount: itemDiscount,
      total
    }

})

const discount = data.discount || 0
const tax = data.tax || 0
const total = subtotal - discount + tax

// Handle loyalty points
let customer = null
let pointsEarned = 0
let pointsUsed = data.pointsUsed || 0

if (data.customerId) {
customer = await prisma.customer.findUnique({
where: { id: data.customerId }
})

    if (!customer) {
      throw new HTTPException(404, { message: 'Customer not found' })
    }

    // Check if customer has enough points to use
    if (pointsUsed > customer.loyaltyPoints) {
      throw new HTTPException(400, { message: 'Insufficient loyalty points' })
    }

    // Calculate points earned (1 point per dollar spent)
    pointsEarned = Math.floor(total)

}

// Create sale transaction
const sale = await prisma.$transaction(async (tx) => {
// Create the sale
const newSale = await tx.sale.create({
data: {
saleNumber,
customerId: data.customerId,
userId: user.id,
subtotal,
tax,
discount,
total,
paymentMethod: data.paymentMethod,
paymentStatus: PaymentStatus.COMPLETED,
pointsEarned,
pointsUsed,
notes: data.notes,
items: {
create: saleItems
}
},
include: {
customer: true,
user: {
select: { firstName: true, lastName: true }
},
items: {
include: {
product: {
select: { name: true, sku: true }
}
}
}
}
})

    // Update product stock and create inventory logs
    for (const item of data.items) {
      const product = products.find(p => p.id === item.productId)!
      const newStock = product.stockQuantity - item.quantity

      await tx.product.update({
        where: { id: item.productId },
        data: { stockQuantity: newStock }
      })

      await tx.inventoryLog.create({
        data: {
          productId: item.productId,
          type: InventoryLogType.SALE,
          quantity: -item.quantity,
          previousQty: product.stockQuantity,
          newQty: newStock,
          reference: saleNumber
        }
      })
    }

    // Update customer loyalty and stats
    if (customer) {
      const newLoyaltyPoints = customer.loyaltyPoints + pointsEarned - pointsUsed
      const newTotalSpent = Number(customer.totalSpent) + total
      const newTotalOrders = customer.totalOrders + 1
      const newAverageOrderValue = newTotalSpent / newTotalOrders

      // Update loyalty tier
      let newTier = customer.loyaltyTier
      if (newLoyaltyPoints >= 10000) newTier = LoyaltyTier.PLATINUM
      else if (newLoyaltyPoints >= 5000) newTier = LoyaltyTier.GOLD
      else if (newLoyaltyPoints >= 1000) newTier = LoyaltyTier.SILVER
      else newTier = LoyaltyTier.BRONZE

      await tx.customer.update({
        where: { id: data.customerId },
        data: {
          loyaltyPoints: newLoyaltyPoints,
          loyaltyTier: newTier,
          totalSpent: newTotalSpent,
          totalOrders: newTotalOrders,
          averageOrderValue: newAverageOrderValue,
          lastPurchaseAt: new Date()
        }
      })
    }

    return newSale

})

return c.json({
message: 'Sale completed successfully',
sale
}, 201)
})

// Refund sale
saleRoutes.post('/:id/refund', authenticateToken, requireManager, zValidator('json', z.object({
reason: z.string().min(1, 'Refund reason is required'),
items: z.array(z.object({
saleItemId: z.string(),
quantity: z.number().int().positive()
})).optional()
})), auditLogger('REFUND', 'sale', ''), async (c) => {
const id = c.req.param('id')
const { reason, items } = c.req.valid('json')

const sale = await prisma.sale.findUnique({
where: { id },
include: {
items: {
include: { product: true }
},
customer: true
}
})

if (!sale) {
throw new HTTPException(404, { message: 'Sale not found' })
}

if (sale.paymentStatus === PaymentStatus.REFUNDED) {
throw new HTTPException(400, { message: 'Sale already refunded' })
}

let refundAmount = Number(sale.total)
let pointsToDeduct = sale.pointsEarned

// Partial refund logic
if (items && items.length > 0) {
refundAmount = 0
for (const refundItem of items) {
const saleItem = sale.items.find(si => si.id === refundItem.saleItemId)
if (!saleItem) continue

      const itemRefundAmount = (Number(saleItem.unitPrice) * refundItem.quantity)
      refundAmount += itemRefundAmount
    }

    // Proportional points deduction
    pointsToDeduct = Math.floor((refundAmount / Number(sale.total)) * sale.pointsEarned)

}

await prisma.$transaction(async (tx) => {
    // Update sale status
    await tx.sale.update({
      where: { id },
      data: { 
        paymentStatus: PaymentStatus.REFUNDED,
        notes: `${sale.notes || ''}\nRefunded: ${reason}`.trim()
}
})

    // Restore inventory
    for (const saleItem of sale.items) {
      let quantityToRestore = saleItem.quantity

      // If partial refund, only restore specified quantities
      if (items && items.length > 0) {
        const refundItem = items.find(ri => ri.saleItemId === saleItem.id)
        quantityToRestore = refundItem?.quantity || 0
      }

      if (quantityToRestore > 0) {
        const newStock = saleItem.product.stockQuantity + quantityToRestore

        await tx.product.update({
          where: { id: saleItem.productId },
          data: { stockQuantity: newStock }
        })

        await tx.inventoryLog.create({
          data: {
            productId: saleItem.productId,
            type: InventoryLogType.RETURN,
            quantity: quantityToRestore,
            previousQty: saleItem.product.stockQuantity,
            newQty: newStock,
            reference: sale.saleNumber,
            reason
          }
        })
      }
    }

    // Update customer points and stats
    if (sale.customer) {
      const newLoyaltyPoints = Math.max(0, sale.customer.loyaltyPoints - pointsToDeduct + sale.pointsUsed)
      const newTotalSpent = Math.max(0, Number(sale.customer.totalSpent) - refundAmount)
      const newTotalOrders = Math.max(0, sale.customer.totalOrders - 1)
      const newAverageOrderValue = newTotalOrders > 0 ? newTotalSpent / newTotalOrders : 0

      await tx.customer.update({
        where: { id: sale.customerId! },
        data: {
          loyaltyPoints: newLoyaltyPoints,
          totalSpent: newTotalSpent,
          totalOrders: newTotalOrders,
          averageOrderValue: newAverageOrderValue
        }
      })
    }

})

return c.json({
message: 'Sale refunded successfully',
refundAmount
})
})

// Get today's sales summary
saleRoutes.get('/summary/today', authenticateToken, requireStaff, async (c) => {
const today = new Date()
today.setHours(0, 0, 0, 0)
const tomorrow = new Date(today)
tomorrow.setDate(tomorrow.getDate() + 1)

const [todaySales, totalSales, averageOrderValue] = await Promise.all([
prisma.sale.aggregate({
where: {
createdAt: { gte: today, lt: tomorrow },
paymentStatus: PaymentStatus.COMPLETED
},
\_sum: { total: true },
\_count: true
}),

    prisma.sale.count({
      where: {
        createdAt: { gte: today, lt: tomorrow },
        paymentStatus: PaymentStatus.COMPLETED
      }
    }),

    prisma.sale.aggregate({
      where: {
        createdAt: { gte: today, lt: tomorrow },
        paymentStatus: PaymentStatus.COMPLETED
      },
      _avg: { total: true }
    })

])

return c.json({
date: today.toISOString().split('T')[0],
totalRevenue: todaySales.\_sum.total || 0,
totalOrders: todaySales.\_count || 0,
averageOrderValue: averageOrderValue.\_avg.total || 0
})
})

// Get sales analytics
saleRoutes.get('/analytics/overview', authenticateToken, requireStaff, async (c) => {
const days = parseInt(c.req.query('days') || '30')
const startDate = new Date()
startDate.setDate(startDate.getDate() - days)

const [salesByDay, topProducts, paymentMethods] = await Promise.all([
// Sales by day
prisma.$queryRaw`      SELECT 
        DATE(created_at) as date,
        COUNT(*)::int as orders,
        SUM(total)::float as revenue
      FROM sales 
      WHERE created_at >= ${startDate} 
        AND payment_status = 'COMPLETED'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
   `,

    // Top selling products
    prisma.saleItem.groupBy({
      by: ['productId'],
      where: {
        sale: {
          createdAt: { gte: startDate },
          paymentStatus: PaymentStatus.COMPLETED
        }
      },
      _sum: { quantity: true, total: true },
      orderBy: { _sum: { total: 'desc' } },
      take: 10
    }),

    // Payment methods breakdown
    prisma.sale.groupBy({
      by: ['paymentMethod'],
      where: {
        createdAt: { gte: startDate },
        paymentStatus: PaymentStatus.COMPLETED
      },
      _sum: { total: true },
      _count: true
    })

])

// Get product details for top products
const productIds = topProducts.map(p => p.productId)
const products = await prisma.product.findMany({
where: { id: { in: productIds } },
select: { id: true, name: true, sku: true }
})

const topProductsWithDetails = topProducts.map(tp => ({
...tp,
product: products.find(p => p.id === tp.productId)
}))

return c.json({
salesByDay,
topProducts: topProductsWithDetails,
paymentMethods
})
})
==============
// src/routes/dashboard.ts
import { Hono } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { prisma } from '../lib/db'
import { authenticateToken, requireStaff } from '../middleware/auth'
import { PaymentStatus, ChurnRisk } from '@prisma/client'

export const dashboardRoutes = new Hono()

// Main dashboard summary
dashboardRoutes.get('/summary', authenticateToken, requireStaff, async (c) => {
const today = new Date()
today.setHours(0, 0, 0, 0)
const yesterday = new Date(today)
yesterday.setDate(yesterday.getDate() - 1)
const lastWeek = new Date(today)
lastWeek.setDate(lastWeek.getDate() - 7)
const lastMonth = new Date(today)
lastMonth.setMonth(lastMonth.getMonth() - 1)

const [
todayStats,
yesterdayStats,
weekStats,
monthStats,
lowStockCount,
totalCustomers,
totalProducts,
atRiskCustomers
] = await Promise.all([
// Today's stats
prisma.sale.aggregate({
where: {
createdAt: { gte: today },
paymentStatus: PaymentStatus.COMPLETED
},
\_sum: { total: true },
\_count: true
}),

    // Yesterday's stats
    prisma.sale.aggregate({
      where: {
        createdAt: { gte: yesterday, lt: today },
        paymentStatus: PaymentStatus.COMPLETED
      },
      _sum: { total: true },
      _count: true
    }),

    // Week stats
    prisma.sale.aggregate({
      where: {
        createdAt: { gte: lastWeek },
        paymentStatus: PaymentStatus.COMPLETED
      },
      _sum: { total: true },
      _count: true
    }),

    // Month stats
    prisma.sale.aggregate({
      where: {
        createdAt: { gte: lastMonth },
        paymentStatus: PaymentStatus.COMPLETED
      },
      _sum: { total: true },
      _count: true
    }),

    // Low stock products count
    prisma.product.count({
      where: {
        isActive: true,
        stockQuantity: { lte: prisma.raw('min_stock_level') }
      }
    }),

    // Total customers
    prisma.customer.count({
      where: { isActive: true }
    }),

    // Total products
    prisma.product.count({
      where: { isActive: true }
    }),

    // At-risk customers count
    prisma.customer.count({
      where: {
        isActive: true,
        churnRisk: { in: [ChurnRisk.HIGH, ChurnRisk.CRITICAL] }
      }
    })

])

// Calculate growth percentages
const todayRevenue = Number(todayStats.\_sum.total || 0)
const yesterdayRevenue = Number(yesterdayStats.\_sum.total || 0)
const revenueGrowth = yesterdayRevenue > 0 ? ((todayRevenue - yesterdayRevenue) / yesterdayRevenue) \* 100 : 0

const todayOrders = todayStats.\_count || 0
const yesterdayOrders = yesterdayStats.\_count || 0
const ordersGrowth = yesterdayOrders > 0 ? ((todayOrders - yesterdayOrders) / yesterdayOrders) \* 100 : 0

return c.json({
today: {
revenue: todayRevenue,
orders: todayOrders,
revenueGrowth,
ordersGrowth
},
week: {
revenue: Number(weekStats.\_sum.total || 0),
orders: weekStats.\_count || 0
},
month: {
revenue: Number(monthStats.\_sum.total || 0),
orders: monthStats.\_count || 0
},
alerts: {
lowStockProducts: lowStockCount,
atRiskCustomers
},
totals: {
customers: totalCustomers,
products: totalProducts
}
})
})

// Recent sales for dashboard
dashboardRoutes.get('/recent-sales', authenticateToken, requireStaff, async (c) => {
const limit = parseInt(c.req.query('limit') || '10')

const sales = await prisma.sale.findMany({
where: {
paymentStatus: PaymentStatus.COMPLETED
},
include: {
customer: {
select: {
firstName: true,
lastName: true
}
},
user: {
select: {
firstName: true,
lastName: true
}
},
\_count: {
select: { items: true }
}
},
orderBy: { createdAt: 'desc' },
take: limit
})

return c.json({ sales })
})

// Sales trends for charts
dashboardRoutes.get('/trends', authenticateToken, requireStaff, async (c) => {
const days = parseInt(c.req.query('days') || '30')
const startDate = new Date()
startDate.setDate(startDate.getDate() - days)

const salesTrends = await prisma.$queryRaw`    SELECT 
      DATE(created_at) as date,
      COUNT(*)::int as orders,
      SUM(total)::float as revenue,
      AVG(total)::float as avg_order_value
    FROM sales 
    WHERE created_at >= ${startDate} 
      AND payment_status = 'COMPLETED'
    GROUP BY DATE(created_at)
    ORDER BY date ASC
 `

return c.json({ trends: salesTrends })
})

// src/routes/ai.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import OpenAI from 'openai'

const openai = new OpenAI({
apiKey: process.env.OPENAI_API_KEY || ''
})

export const aiRoutes = new Hono()

// Generate product description
aiRoutes.post('/generate-description', authenticateToken, requireManager, zValidator('json', z.object({
productName: z.string().min(1, 'Product name is required'),
category: z.string().optional(),
features: z.array(z.string()).optional(),
targetAudience: z.string().optional()
})), async (c) => {
const { productName, category, features, targetAudience } = c.req.valid('json')

if (!process.env.OPENAI_API_KEY) {
throw new HTTPException(500, { message: 'OpenAI API key not configured' })
}

try {
const prompt = `Generate a compelling product description for an e-commerce store:

Product Name: ${productName}
${category ? `Category: ${category}` : ''}
${features ? `Features: ${features.join(', ')}` : ''}
${targetAudience ? `Target Audience: ${targetAudience}` : ''}

Write a professional, engaging product description that:

- Highlights key benefits and features
- Uses persuasive language
- Is SEO-friendly
- Is between 100-200 words
- Appeals to the target audience`

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 300,
        temperature: 0.7
      })

      const description = response.choices[0]?.message?.content?.trim()

      if (!description) {
        throw new HTTPException(500, { message: 'Failed to generate description' })
      }

      return c.json({ description })

  } catch (error) {
  console.error('OpenAI API error:', error)
  throw new HTTPException(500, { message: 'Failed to generate description' })
  }
  })

// Sales forecast
aiRoutes.get('/forecast/sales', authenticateToken, requireStaff, async (c) => {
const days = parseInt(c.req.query('days') || '30')

try {
// Get historical sales data
const historicalData = await prisma.$queryRaw`      SELECT 
        DATE(created_at) as date,
        SUM(total)::float as revenue,
        COUNT(*)::int as orders
      FROM sales 
      WHERE created_at >= NOW() - INTERVAL '90 days'
        AND payment_status = 'COMPLETED'
      GROUP BY DATE(created_at)
      ORDER BY date ASC
   ` as Array<{ date: Date; revenue: number; orders: number }>

    if (historicalData.length < 7) {
      return c.json({
        forecast: [],
        message: 'Insufficient historical data for accurate forecasting'
      })
    }

    // Simple moving average forecast
    const movingAverageWindow = 7
    const recentRevenues = historicalData.slice(-movingAverageWindow).map(d => d.revenue)
    const averageRevenue = recentRevenues.reduce((a, b) => a + b, 0) / recentRevenues.length

    const recentOrders = historicalData.slice(-movingAverageWindow).map(d => d.orders)
    const averageOrders = recentOrders.reduce((a, b) => a + b, 0) / recentOrders.length

    // Generate forecast for next N days
    const forecast = []
    const today = new Date()

    for (let i = 1; i <= days; i++) {
      const forecastDate = new Date(today)
      forecastDate.setDate(today.getDate() + i)

      // Add some variance (±15%)
      const revenueVariance = 0.85 + (Math.random() * 0.3)
      const ordersVariance = 0.85 + (Math.random() * 0.3)

      forecast.push({
        date: forecastDate.toISOString().split('T')[0],
        predictedRevenue: Math.round(averageRevenue * revenueVariance * 100) / 100,
        predictedOrders: Math.round(averageOrders * ordersVariance),
        confidence: Math.random() * 0.3 + 0.7 // 70-100% confidence
      })
    }

    return c.json({ forecast, historicalData })

} catch (error) {
console.error('Sales forecast error:', error)
throw new HTTPException(500, { message: 'Failed to generate sales forecast' })
}
})

// Predict customer churn risk
aiRoutes.post('/predict-churn', authenticateToken, requireManager, async (c) => {
try {
const customers = await prisma.customer.findMany({
where: { isActive: true },
include: {
sales: {
select: {
createdAt: true,
total: true
},
orderBy: { createdAt: 'desc' }
}
}
})

    const updates = []

    for (const customer of customers) {
      const sales = customer.sales

      if (sales.length === 0) {
        updates.push({ id: customer.id, churnRisk: ChurnRisk.CRITICAL })
        continue
      }

      const lastPurchase = sales[0].createdAt
      const daysSinceLastPurchase = Math.floor((Date.now() - lastPurchase.getTime()) / (1000 * 60 * 60 * 24))

      // Simple churn risk calculation
      let churnRisk = ChurnRisk.LOW

      if (daysSinceLastPurchase > 90) {
        churnRisk = ChurnRisk.CRITICAL
      } else if (daysSinceLastPurchase > 60) {
        churnRisk = ChurnRisk.HIGH
      } else if (daysSinceLastPurchase > 30) {
        churnRisk = ChurnRisk.MEDIUM
      }

      // Consider purchase frequency
      if (sales.length < 2 && daysSinceLastPurchase > 30) {
        churnRisk = ChurnRisk.HIGH
      }

      if (customer.churnRisk !== churnRisk) {
        updates.push({ id: customer.id, churnRisk })
      }
    }

    // Batch update customers
    for (const update of updates) {
      await prisma.customer.update({
        where: { id: update.id },
        data: { churnRisk: update.churnRisk }
      })
    }

    return c.json({
      message: `Updated churn risk for ${updates.length} customers`,
      updatedCount: updates.length
    })

} catch (error) {
console.error('Churn prediction error:', error)
throw new HTTPException(500, { message: 'Failed to predict customer churn' })
}
})

// Product recommendations for POS
aiRoutes.get('/recommendations/:customerId', authenticateToken, requireStaff, async (c) => {
const customerId = c.req.param('customerId')
const limit = parseInt(c.req.query('limit') || '5')

try {
const customer = await prisma.customer.findUnique({
where: { id: customerId },
include: {
sales: {
include: {
items: {
include: {
product: {
select: {
id: true,
name: true,
categoryId: true
}
}
}
}
}
}
}
})

    if (!customer) {
      throw new HTTPException(404, { message: 'Customer not found' })
    }

    // Get customer's purchase history
    const purchasedProductIds = new Set()
    const purchasedCategories = new Set()

    customer.sales.forEach(sale => {
      sale.items.forEach(item => {
        purchasedProductIds.add(item.productId)
        if (item.product.categoryId) {
          purchasedCategories.add(item.product.categoryId)
        }
      })
    })

    // Find recommendations based on:
    // 1. Same categories as previously purchased
    // 2. Popular products not yet purchased
    // 3. High-margin products

    const recommendations = await prisma.product.findMany({
      where: {
        isActive: true,
        stockQuantity: { gt: 0 },
        id: { notIn: Array.from(purchasedProductIds) as string[] },
        OR: [
          // Products from same categories
          { categoryId: { in: Array.from(purchasedCategories) as string[] } },
          // Featured products
          { isFeatured: true }
        ]
      },
      include: {
        category: {
          select: { name: true }
        },
        _count: {
          select: { saleItems: true }
        }
      },
      orderBy: [
        { isFeatured: 'desc' },
        { markup: 'desc' },
        { saleItems: { _count: 'desc' } }
      ],
      take: limit
    })

    return c.json({ recommendations })

} catch (error) {
console.error('Recommendations error:', error)
throw new HTTPException(500, { message: 'Failed to get recommendations' })
}
})

// Stock prediction
aiRoutes.get('/predict-stock/:productId', authenticateToken, requireManager, async (c) => {
const productId = c.req.param('productId')
const days = parseInt(c.req.query('days') || '30')

try {
const product = await prisma.product.findUnique({
where: { id: productId },
include: {
saleItems: {
where: {
sale: {
createdAt: { gte: new Date(Date.now() - 30 _ 24 _ 60 _ 60 _ 1000) }
}
},
select: {
quantity: true,
sale: {
select: { createdAt: true }
}
}
}
}
})

    if (!product) {
      throw new HTTPException(404, { message: 'Product not found' })
    }

    // Calculate daily average sales
    const dailySales = product.saleItems.reduce((acc, item) => {
      const date = item.sale.createdAt.toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + item.quantity
      return acc
    }, {} as Record<string, number>)

    const totalSold = Object.values(dailySales).reduce((a, b) => a + b, 0)
    const averageDailySales = totalSold / 30 || 0

    const currentStock = product.stockQuantity
    const daysUntilStockout = averageDailySales > 0 ? Math.floor(currentStock / averageDailySales) : null
    const predictedStockoutDate = daysUntilStockout ?
      new Date(Date.now() + daysUntilStockout * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null

    // Generate future stock predictions
    const predictions = []
    for (let i = 1; i <= days; i++) {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + i)

      const predictedStock = Math.max(0, currentStock - (averageDailySales * i))

      predictions.push({
        date: futureDate.toISOString().split('T')[0],
        predictedStock: Math.round(predictedStock),
        salesVelocity: averageDailySales
      })
    }

    return c.json({
      product: {
        id: product.id,
        name: product.name,
        currentStock,
        minStockLevel: product.minStockLevel,
        reorderPoint: product.reorderPoint
      },
      analytics: {
        averageDailySales: Math.round(averageDailySales * 100) / 100,
        daysUntilStockout,
        predictedStockoutDate,
        reorderNeeded: currentStock <= product.reorderPoint
      },
      predictions
    })

} catch (error) {
console.error('Stock prediction error:', error)
throw new HTTPException(500, { message: 'Failed to predict stock levels' })
}
})

// Generate business insights
aiRoutes.get('/insights', authenticateToken, requireManager, async (c) => {
try {
const [
lowStockProducts,
topSellingProducts,
slowMovingProducts,
highValueCustomers,
salesTrends
] = await Promise.all([
// Low stock products
prisma.product.count({
where: {
isActive: true,
stockQuantity: { lte: prisma.raw('min_stock_level') }
}
}),

      // Top selling products this month
      prisma.saleItem.groupBy({
        by: ['productId'],
        where: {
          sale: {
            createdAt: { gte: new Date(new Date().setDate(1)) }
          }
        },
        _sum: { quantity: true },
        orderBy: { _sum: { quantity: 'desc' } },
        take: 5
      }),

      // Slow moving products (no sales in 30 days)
      prisma.product.count({
        where: {
          isActive: true,
          saleItems: {
            none: {
              sale: {
                createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
              }
            }
          }
        }
      }),

      // High value customers
      prisma.customer.count({
        where: {
          isActive: true,
          totalSpent: { gte: 1000 }
        }
      }),

      // Sales trend (this week vs last week)
      prisma.$queryRaw`
        SELECT
          CASE
            WHEN created_at >= NOW() - INTERVAL '7 days' THEN 'this_week'
            ELSE 'last_week'
          END as period,
          SUM(total)::float as revenue,
          COUNT(*)::int as orders
        FROM sales
        WHERE created_at >= NOW() - INTERVAL '14 days'
          AND payment_status = 'COMPLETED'
        GROUP BY period
      ` as Array<{ period: string; revenue: number; orders: number }>
    ])

    const thisWeek = salesTrends.find(t => t.period === 'this_week')
    const lastWeek = salesTrends.find(t => t.period === 'last_week')

    const revenueGrowth = lastWeek && thisWeek ?
      ((thisWeek.revenue - lastWeek.revenue) / lastWeek.revenue) * 100 : 0

    const insights = [
      {
        type: 'warning',
        title: 'Low Stock Alert',
        message: `${lowStockProducts} products are running low on stock`,
        priority: 'high',
        actionable: lowStockProducts > 0
      },
      {
        type: 'info',
        title: 'Slow Moving Inventory',
        message: `${slowMovingProducts} products haven't sold in the last 30 days`,
        priority: 'medium',
        actionable: slowMovingProducts > 0
      },
      {
        type: 'success',
        title: 'High Value Customers',
        message: `You have ${highValueCustomers} customers who have spent over $1000`,
        priority: 'low',
        actionable: false
      },
      {
        type: revenueGrowth > 0 ? 'success' : 'warning',
        title: 'Sales Trend',
        message: `Revenue is ${revenueGrowth > 0 ? 'up' : 'down'} ${Math.abs(revenueGrowth).toFixed(1)}% compared to last week`,
        priority: Math.abs(revenueGrowth) > 10 ? 'high' : 'medium',
        actionable: revenueGrowth < -10
      }
    ]

    return c.json({ insights })

} catch (error) {
console.error('Insights generation error:', error)
throw new HTTPException(500, { message: 'Failed to generate insights' })
}
})
===============
// src/routes/inventory.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { InventoryLogType } from '@prisma/client'

export const inventoryRoutes = new Hono()

// Get inventory overview
inventoryRoutes.get('/overview', authenticateToken, requireStaff, async (c) => {
const [
totalProducts,
lowStockCount,
outOfStockCount,
totalValue,
recentLogs
] = await Promise.all([
prisma.product.count({ where: { isActive: true } }),

    prisma.product.count({
      where: {
        isActive: true,
        stockQuantity: { lte: prisma.raw('min_stock_level') }
      }
    }),

    prisma.product.count({
      where: {
        isActive: true,
        stockQuantity: 0
      }
    }),

    prisma.product.aggregate({
      where: { isActive: true },
      _sum: {
        stockQuantity: true
      }
    }),

    prisma.inventoryLog.findMany({
      include: {
        product: {
          select: { name: true, sku: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

])

const averageValue = await prisma.$queryRaw`    SELECT SUM(stock_quantity * cost)::float as total_value
    FROM products 
    WHERE is_active = true
 ` as [{ total_value: number }]

return c.json({
summary: {
totalProducts,
lowStockCount,
outOfStockCount,
totalStockValue: averageValue[0]?.total_value || 0
},
recentLogs
})
})

// Get inventory logs with filters
inventoryRoutes.get('/logs', authenticateToken, requireStaff, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const productId = c.req.query('productId')
const type = c.req.query('type') as InventoryLogType
const startDate = c.req.query('startDate')
const endDate = c.req.query('endDate')

const skip = (page - 1) \* limit

const where = {
...(productId && { productId }),
...(type && { type }),
...(startDate && endDate && {
createdAt: {
gte: new Date(startDate),
lte: new Date(endDate)
}
})
}

const [logs, total] = await Promise.all([
prisma.inventoryLog.findMany({
where,
include: {
product: {
select: { name: true, sku: true }
}
},
orderBy: { createdAt: 'desc' },
skip,
take: limit
}),
prisma.inventoryLog.count({ where })
])

return c.json({
logs,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Bulk stock adjustment
inventoryRoutes.post('/bulk-adjust', authenticateToken, requireManager, zValidator('json', z.object({
adjustments: z.array(z.object({
productId: z.string(),
quantity: z.number().int(),
reason: z.string().min(1, 'Reason is required')
}))
})), async (c) => {
const { adjustments } = c.req.valid('json')

const results = await prisma.$transaction(async (tx) => {
const updateResults = []

    for (const adjustment of adjustments) {
      const product = await tx.product.findUnique({
        where: { id: adjustment.productId }
      })

      if (!product) {
        updateResults.push({
          productId: adjustment.productId,
          success: false,
          error: 'Product not found'
        })
        continue
      }

      const newQuantity = Math.max(0, product.stockQuantity + adjustment.quantity)

      await tx.product.update({
        where: { id: adjustment.productId },
        data: { stockQuantity: newQuantity }
      })

      await tx.inventoryLog.create({
        data: {
          productId: adjustment.productId,
          type: InventoryLogType.ADJUSTMENT,
          quantity: adjustment.quantity,
          previousQty: product.stockQuantity,
          newQty: newQuantity,
          reason: adjustment.reason
        }
      })

      updateResults.push({
        productId: adjustment.productId,
        success: true,
        newQuantity
      })
    }

    return updateResults

})

const successCount = results.filter(r => r.success).length

return c.json({
message: `${successCount} of ${adjustments.length} products updated successfully`,
results
})
})

// src/routes/suppliers.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { authenticateToken, requireManager } from '../middleware/auth'

const supplierSchema = z.object({
name: z.string().min(1, 'Supplier name is required'),
email: z.string().email().optional(),
phone: z.string().optional(),
address: z.string().optional(),
city: z.string().optional(),
state: z.string().optional(),
zipCode: z.string().optional(),
country: z.string().optional(),
contactPerson: z.string().optional(),
website: z.string().url().optional(),
notes: z.string().optional()
})

export const supplierRoutes = new Hono()

// Get all suppliers
supplierRoutes.get('/', authenticateToken, requireManager, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const search = c.req.query('search') || ''

const skip = (page - 1) \* limit

const where = {
isActive: true,
...(search && {
OR: [
{ name: { contains: search, mode: 'insensitive' as const } },
{ email: { contains: search, mode: 'insensitive' as const } },
{ contactPerson: { contains: search, mode: 'insensitive' as const } }
]
})
}

const [suppliers, total] = await Promise.all([
prisma.supplier.findMany({
where,
include: {
_count: {
select: { products: true, purchaseOrders: true }
}
},
orderBy: { name: 'asc' },
skip,
take: limit
}),
prisma.supplier.count({ where })
])

return c.json({
suppliers,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Create supplier
supplierRoutes.post('/', authenticateToken, requireManager, zValidator('json', supplierSchema), async (c) => {
const data = c.req.valid('json')

const supplier = await prisma.supplier.create({
data
})

return c.json({
message: 'Supplier created successfully',
supplier
}, 201)
})

// Update supplier
supplierRoutes.put('/:id', authenticateToken, requireManager, zValidator('json', supplierSchema.partial()), async (c) => {
const id = c.req.param('id')
const data = c.req.valid('json')

const supplier = await prisma.supplier.update({
where: { id },
data
})

return c.json({
message: 'Supplier updated successfully',
supplier
})
})

// src/routes/reports.ts
import { Hono } from 'hono'
import { prisma } from '../lib/db'
import { authenticateToken, requireStaff, requireManager } from '../middleware/auth'
import { PaymentStatus, PaymentMethod } from '@prisma/client'

export const reportRoutes = new Hono()

// Sales report
reportRoutes.get('/sales', authenticateToken, requireStaff, async (c) => {
const startDate = c.req.query('startDate')
const endDate = c.req.query('endDate')
const groupBy = c.req.query('groupBy') || 'day' // day, week, month
const userId = c.req.query('userId')
const paymentMethod = c.req.query('paymentMethod') as PaymentMethod

if (!startDate || !endDate) {
throw new HTTPException(400, { message: 'Start date and end date are required' })
}

const where = {
createdAt: {
gte: new Date(startDate),
lte: new Date(endDate)
},
paymentStatus: PaymentStatus.COMPLETED,
...(userId && { userId }),
...(paymentMethod && { paymentMethod })
}

const [
totalSales,
salesByPeriod,
topProducts,
paymentMethodBreakdown
] = await Promise.all([
// Total sales summary
prisma.sale.aggregate({
where,
\_sum: { total: true, tax: true, discount: true },
\_count: true,
\_avg: { total: true }
}),

    // Sales grouped by period
    prisma.$queryRaw`
      SELECT
        DATE_TRUNC(${groupBy}, created_at) as period,
        COUNT(*)::int as orders,
        SUM(total)::float as revenue,
        AVG(total)::float as avg_order_value
      FROM sales
      WHERE created_at BETWEEN ${new Date(startDate)} AND ${new Date(endDate)}
        AND payment_status = 'COMPLETED'
        ${userId ? prisma.raw`AND user_id = ${userId}` : prisma.raw``}
        ${paymentMethod ? prisma.raw`AND payment_method = ${paymentMethod}` : prisma.raw``}
      GROUP BY period
      ORDER BY period ASC
    `,

    // Top selling products
    prisma.saleItem.groupBy({
      by: ['productId'],
      where: {
        sale: where
      },
      _sum: { quantity: true, total: true },
      orderBy: { _sum: { total: 'desc' } },
      take: 10
    }),

    // Payment method breakdown
    prisma.sale.groupBy({
      by: ['paymentMethod'],
      where,
      _sum: { total: true },
      _count: true
    })

])

// Get product details for top products
const productIds = topProducts.map(p => p.productId)
const products = await prisma.product.findMany({
where: { id: { in: productIds } },
select: { id: true, name: true, sku: true }
})

const topProductsWithDetails = topProducts.map(tp => ({
...tp,
product: products.find(p => p.id === tp.productId)
}))

return c.json({
summary: {
totalRevenue: totalSales.\_sum.total || 0,
totalOrders: totalSales.\_count || 0,
averageOrderValue: totalSales.\_avg.total || 0,
totalTax: totalSales.\_sum.tax || 0,
totalDiscount: totalSales.\_sum.discount || 0
},
salesByPeriod,
topProducts: topProductsWithDetails,
paymentMethodBreakdown
})
})

// Inventory report
reportRoutes.get('/inventory', authenticateToken, requireStaff, async (c) => {
const [
inventorySummary,
lowStockProducts,
topValueProducts,
categoryBreakdown
] = await Promise.all([
// Inventory summary
prisma.$queryRaw`      SELECT 
        COUNT(*)::int as total_products,
        SUM(stock_quantity)::int as total_stock,
        SUM(stock_quantity * cost)::float as total_value,
        COUNT(CASE WHEN stock_quantity <= min_stock_level THEN 1 END)::int as low_stock_count,
        COUNT(CASE WHEN stock_quantity = 0 THEN 1 END)::int as out_of_stock_count
      FROM products 
      WHERE is_active = true
   ` as [any],

    // Low stock products
    prisma.product.findMany({
      where: {
        isActive: true,
        stockQuantity: { lte: prisma.raw('min_stock_level') }
      },
      select: {
        id: true,
        name: true,
        sku: true,
        stockQuantity: true,
        minStockLevel: true,
        cost: true,
        price: true
      },
      orderBy: { stockQuantity: 'asc' }
    }),

    // Top value products
    prisma.$queryRaw`
      SELECT
        id,
        name,
        sku,
        stock_quantity,
        cost,
        price,
        (stock_quantity * cost)::float as total_value
      FROM products
      WHERE is_active = true AND stock_quantity > 0
      ORDER BY total_value DESC
      LIMIT 10
    `,

    // Category breakdown
    prisma.$queryRaw`
      SELECT
        c.name as category_name,
        COUNT(p.id)::int as product_count,
        SUM(p.stock_quantity)::int as total_stock,
        SUM(p.stock_quantity * p.cost)::float as total_value
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = true
      GROUP BY c.name
      ORDER BY total_value DESC
    `

])

return c.json({
summary: inventorySummary[0],
lowStockProducts,
topValueProducts,
categoryBreakdown
})
})

// Customer report
reportRoutes.get('/customers', authenticateToken, requireStaff, async (c) => {
const startDate = c.req.query('startDate')
const endDate = c.req.query('endDate')

const dateFilter = startDate && endDate ? {
createdAt: {
gte: new Date(startDate),
lte: new Date(endDate)
}
} : {}

const [
customerSummary,
topCustomers,
customerSegments,
churnAnalysis
] = await Promise.all([
// Customer summary
prisma.customer.aggregate({
where: { isActive: true, ...dateFilter },
\_count: true,
\_sum: { totalSpent: true, loyaltyPoints: true },
\_avg: { averageOrderValue: true }
}),

    // Top customers by spending
    prisma.customer.findMany({
      where: { isActive: true },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        totalSpent: true,
        totalOrders: true,
        loyaltyTier: true,
        lastPurchaseAt: true
      },
      orderBy: { totalSpent: 'desc' },
      take: 10
    }),

    // Customer segments
    prisma.customer.groupBy({
      by: ['loyaltyTier'],
      where: { isActive: true },
      _count: true,
      _sum: { totalSpent: true },
      _avg: { averageOrderValue: true }
    }),

    // Churn analysis
    prisma.customer.groupBy({
      by: ['churnRisk'],
      where: { isActive: true },
      _count: true
    })

])

return c.json({
summary: customerSummary,
topCustomers,
customerSegments,
churnAnalysis
})
})

// src/routes/settings.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { authenticateToken, requireAdmin, requireManager } from '../middleware/auth'
import { SettingType } from '@prisma/client'

export const settingRoutes = new Hono()

// Get all settings
settingRoutes.get('/', authenticateToken, requireManager, async (c) => {
const settings = await prisma.setting.findMany({
orderBy: { key: 'asc' }
})

// Convert to key-value object
const settingsObject = settings.reduce((acc, setting) => {
let value = setting.value

    // Parse values based on type
    if (setting.type === SettingType.BOOLEAN) {
      value = setting.value === 'true'
    } else if (setting.type === SettingType.NUMBER) {
      value = parseFloat(setting.value)
    } else if (setting.type === SettingType.JSON) {
      try {
        value = JSON.parse(setting.value)
      } catch (e) {
        value = setting.value
      }
    }

    acc[setting.key] = value
    return acc

}, {} as Record<string, any>)

return c.json({ settings: settingsObject })
})

// Update settings
settingRoutes.put('/', authenticateToken, requireAdmin, zValidator('json', z.object({
settings: z.record(z.any())
})), async (c) => {
const { settings } = c.req.valid('json')

const updates = []

for (const [key, value] of Object.entries(settings)) {
let stringValue: string
let type: SettingType = SettingType.STRING

    if (typeof value === 'boolean') {
      stringValue = value.toString()
      type = SettingType.BOOLEAN
    } else if (typeof value === 'number') {
      stringValue = value.toString()
      type = SettingType.NUMBER
    } else if (typeof value === 'object') {
      stringValue = JSON.stringify(value)
      type = SettingType.JSON
    } else {
      stringValue = String(value)
    }

    updates.push(
      prisma.setting.upsert({
        where: { key },
        create: { key, value: stringValue, type },
        update: { value: stringValue, type }
      })
    )

}

await Promise.all(updates)

return c.json({ message: 'Settings updated successfully' })
})

// src/routes/users.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { authenticateToken, requireAdmin, requireManager } from '../middleware/auth'
import { registerSchema } from '../lib/validation'

export const userRoutes = new Hono()

// Get all users
userRoutes.get('/', authenticateToken, requireManager, async (c) => {
const users = await prisma.user.findMany({
select: {
id: true,
email: true,
firstName: true,
lastName: true,
role: true,
isActive: true,
createdAt: true
},
orderBy: { createdAt: 'desc' }
})

return c.json({ users })
})

// Update user
userRoutes.put('/:id', authenticateToken, requireAdmin, zValidator('json', registerSchema.partial().omit({ password: true })), async (c) => {
const id = c.req.param('id')
const data = c.req.valid('json')

const user = await prisma.user.update({
where: { id },
data,
select: {
id: true,
email: true,
firstName: true,
lastName: true,
role: true,
isActive: true
}
})

return c.json({
message: 'User updated successfully',
user
})
})

// Deactivate user
userRoutes.delete('/:id', authenticateToken, requireAdmin, async (c) => {
const id = c.req.param('id')
const currentUser = c.get('user')

if (id === currentUser.id) {
throw new HTTPException(400, { message: 'Cannot deactivate your own account' })
}

await prisma.user.update({
where: { id },
data: { isActive: false }
})

return c.json({ message: 'User deactivated successfully' })
})

// src/routes/audit.ts
export const auditRoutes = new Hono()

// Get audit logs
auditRoutes.get('/logs', authenticateToken, requireAdmin, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '50')
const action = c.req.query('action')
const entity = c.req.query('entity')
const userId = c.req.query('userId')

const skip = (page - 1) \* limit

const where = {
...(action && { action: { contains: action, mode: 'insensitive' as const } }),
...(entity && { entity }),
...(userId && { userId })
}

const [logs, total] = await Promise.all([
prisma.auditLog.findMany({
where,
include: {
user: {
select: { firstName: true, lastName: true, email: true }
}
},
orderBy: { createdAt: 'desc' },
skip,
take: limit
}),
prisma.auditLog.count({ where })
])

return c.json({
logs,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})
==============================
// src/routes/purchase-orders.ts
import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { z } from 'zod'
import { prisma } from '../lib/db'
import { authenticateToken, requireManager } from '../middleware/auth'
import { auditLogger } from '../middleware/logging'
import { PurchaseOrderStatus, InventoryLogType } from '@prisma/client'

const purchaseOrderSchema = z.object({
supplierId: z.string().min(1, 'Supplier is required'),
expectedDate: z.string().transform(str => new Date(str)).optional(),
items: z.array(z.object({
productId: z.string(),
quantity: z.number().int().positive(),
unitCost: z.number().positive()
})),
notes: z.string().optional()
})

export const purchaseOrderRoutes = new Hono()

// Get all purchase orders
purchaseOrderRoutes.get('/', authenticateToken, requireManager, async (c) => {
const page = parseInt(c.req.query('page') || '1')
const limit = parseInt(c.req.query('limit') || '20')
const status = c.req.query('status') as PurchaseOrderStatus
const supplierId = c.req.query('supplierId')

const skip = (page - 1) \* limit

const where = {
...(status && { status }),
...(supplierId && { supplierId })
}

const [orders, total] = await Promise.all([
prisma.purchaseOrder.findMany({
where,
include: {
supplier: {
select: { name: true, email: true }
},
items: {
include: {
product: {
select: { name: true, sku: true }
}
}
},
_count: {
select: { items: true }
}
},
orderBy: { createdAt: 'desc' },
skip,
take: limit
}),
prisma.purchaseOrder.count({ where })
])

return c.json({
orders,
pagination: {
page,
limit,
total,
totalPages: Math.ceil(total / limit)
}
})
})

// Get single purchase order
purchaseOrderRoutes.get('/:id', authenticateToken, requireManager, async (c) => {
const id = c.req.param('id')

const order = await prisma.purchaseOrder.findUnique({
where: { id },
include: {
supplier: true,
items: {
include: {
product: true
}
}
}
})

if (!order) {
throw new HTTPException(404, { message: 'Purchase order not found' })
}

return c.json({ order })
})

// Create purchase order
purchaseOrderRoutes.post('/', authenticateToken, requireManager, zValidator('json', purchaseOrderSchema), auditLogger('CREATE', 'purchase_order', ''), async (c) => {
const data = c.req.valid('json')

// Generate order number
const orderCount = await prisma.purchaseOrder.count()
const orderNumber = `PO-${Date.now()}-${String(orderCount + 1).padStart(4, '0')}`

// Calculate totals
let subtotal = 0
const orderItems = data.items.map(item => {
const total = item.unitCost \* item.quantity
subtotal += total
return {
...item,
totalCost: total
}
})

const tax = subtotal \* 0.1 // 10% tax (configurable)
const total = subtotal + tax

const order = await prisma.purchaseOrder.create({
data: {
orderNumber,
supplierId: data.supplierId,
expectedDate: data.expectedDate,
subtotal,
tax,
total,
notes: data.notes,
items: {
create: orderItems
}
},
include: {
supplier: true,
items: {
include: {
product: {
select: { name: true, sku: true }
}
}
}
}
})

return c.json({
message: 'Purchase order created successfully',
order
}, 201)
})

// Update purchase order status
purchaseOrderRoutes.patch('/:id/status', authenticateToken, requireManager, zValidator('json', z.object({
status: z.nativeEnum(PurchaseOrderStatus),
notes: z.string().optional()
})), auditLogger('UPDATE', 'purchase_order', ''), async (c) => {
const id = c.req.param('id')
const { status, notes } = c.req.valid('json')

const order = await prisma.purchaseOrder.findUnique({
where: { id },
include: { items: { include: { product: true } } }
})

if (!order) {
throw new HTTPException(404, { message: 'Purchase order not found' })
}

const updateData: any = { status }

if (notes) {
updateData.notes = `${order.notes || ''}\n${notes}`.trim()
}

if (status === PurchaseOrderStatus.RECEIVED) {
updateData.receivedDate = new Date()
}

const updatedOrder = await prisma.$transaction(async (tx) => {
// Update the purchase order
const updated = await tx.purchaseOrder.update({
where: { id },
data: updateData,
include: {
supplier: true,
items: {
include: {
product: {
select: { name: true, sku: true }
}
}
}
}
})

    // If marked as received, update inventory
    if (status === PurchaseOrderStatus.RECEIVED) {
      for (const item of order.items) {
        const newStock = item.product.stockQuantity + item.quantity

        await tx.product.update({
          where: { id: item.productId },
          data: { stockQuantity: newStock }
        })

        await tx.inventoryLog.create({
          data: {
            productId: item.productId,
            type: InventoryLogType.PURCHASE,
            quantity: item.quantity,
            previousQty: item.product.stockQuantity,
            newQty: newStock,
            reference: order.orderNumber
          }
        })
      }
    }

    return updated

})

return c.json({
message: 'Purchase order updated successfully',
order: updatedOrder
})
})

// Receive items (partial receiving)
purchaseOrderRoutes.post('/:id/receive', authenticateToken, requireManager, zValidator('json', z.object({
items: z.array(z.object({
itemId: z.string(),
receivedQuantity: z.number().int().min(0)
}))
})), auditLogger('RECEIVE', 'purchase_order', ''), async (c) => {
const id = c.req.param('id')
const { items } = c.req.valid('json')

const order = await prisma.purchaseOrder.findUnique({
where: { id },
include: {
items: {
include: { product: true }
}
}
})

if (!order) {
throw new HTTPException(404, { message: 'Purchase order not found' })
}

await prisma.$transaction(async (tx) => {
for (const receivedItem of items) {
const orderItem = order.items.find(item => item.id === receivedItem.itemId)
if (!orderItem || receivedItem.receivedQuantity <= 0) continue

      // Update received quantity
      await tx.purchaseOrderItem.update({
        where: { id: receivedItem.itemId },
        data: {
          receivedQty: orderItem.receivedQty + receivedItem.receivedQuantity
        }
      })

      // Update product stock
      const newStock = orderItem.product.stockQuantity + receivedItem.receivedQuantity

      await tx.product.update({
        where: { id: orderItem.productId },
        data: { stockQuantity: newStock }
      })

      // Create inventory log
      await tx.inventoryLog.create({
        data: {
          productId: orderItem.productId,
          type: InventoryLogType.PURCHASE,
          quantity: receivedItem.receivedQuantity,
          previousQty: orderItem.product.stockQuantity,
          newQty: newStock,
          reference: order.orderNumber,
          reason: 'Partial receipt'
        }
      })
    }

    // Check if all items are fully received
    const updatedItems = await tx.purchaseOrderItem.findMany({
      where: { purchaseOrderId: id }
    })

    const allReceived = updatedItems.every(item => item.receivedQty >= item.quantity)

    if (allReceived) {
      await tx.purchaseOrder.update({
        where: { id },
        data: {
          status: PurchaseOrderStatus.RECEIVED,
          receivedDate: new Date()
        }
      })
    }

})

return c.json({ message: 'Items received successfully' })
})

// src/scripts/seed.ts
import { PrismaClient, UserRole, LoyaltyTier, ChurnRisk, PaymentMethod, PaymentStatus, SettingType } from '@prisma/client'
import { hashPassword } from '../lib/auth'

const prisma = new PrismaClient()

async function main() {
console.log('🌱 Starting database seed...')

// Create admin user
const adminPassword = await hashPassword('admin123')
const admin = await prisma.user.upsert({
where: { email: '<EMAIL>' },
update: {},
create: {
email: '<EMAIL>',
password: adminPassword,
firstName: 'Admin',
lastName: 'User',
role: UserRole.ADMIN
}
})
console.log('✅ Admin user created')

// Create staff user
const staffPassword = await hashPassword('staff123')
const staff = await prisma.user.upsert({
where: { email: '<EMAIL>' },
update: {},
create: {
email: '<EMAIL>',
password: staffPassword,
firstName: 'Staff',
lastName: 'Member',
role: UserRole.STAFF
}
})
console.log('✅ Staff user created')

// Create categories
const electronics = await prisma.category.upsert({
where: { slug: 'electronics' },
update: {},
create: {
name: 'Electronics',
description: 'Electronic devices and accessories',
slug: 'electronics'
}
})

const clothing = await prisma.category.upsert({
where: { slug: 'clothing' },
update: {},
create: {
name: 'Clothing',
description: 'Apparel and fashion items',
slug: 'clothing'
}
})

const books = await prisma.category.upsert({
where: { slug: 'books' },
update: {},
create: {
name: 'Books',
description: 'Books and educational materials',
slug: 'books'
}
})
console.log('✅ Categories created')

// Create suppliers
const techSupplier = await prisma.supplier.create({
data: {
name: 'TechSupply Co.',
email: '<EMAIL>',
phone: '******-0123',
address: '123 Tech Street',
city: 'San Francisco',
state: 'CA',
zipCode: '94105',
country: 'USA',
contactPerson: 'John Doe'
}
})

const fashionSupplier = await prisma.supplier.create({
data: {
name: 'Fashion Wholesale',
email: '<EMAIL>',
phone: '******-0456',
address: '456 Fashion Ave',
city: 'New York',
state: 'NY',
zipCode: '10001',
country: 'USA',
contactPerson: 'Jane Smith'
}
})
console.log('✅ Suppliers created')

// Create products
const products = [
{
name: 'Wireless Bluetooth Headphones',
description: 'High-quality wireless headphones with noise cancellation',
sku: 'WBH-001',
barcode: '1234567890123',
price: 199.99,
cost: 120.00,
stockQuantity: 50,
minStockLevel: 10,
categoryId: electronics.id,
createdById: admin.id,
tags: ['wireless', 'bluetooth', 'audio']
},
{
name: 'Cotton T-Shirt',
description: 'Comfortable 100% cotton t-shirt',
sku: 'CTS-001',
barcode: '1234567890124',
price: 29.99,
cost: 15.00,
stockQuantity: 100,
minStockLevel: 20,
categoryId: clothing.id,
createdById: admin.id,
tags: ['cotton', 'casual', 'comfortable']
},
{
name: 'JavaScript: The Good Parts',
description: 'Essential JavaScript programming guide',
sku: 'JSB-001',
barcode: '1234567890125',
price: 34.99,
cost: 20.00,
stockQuantity: 25,
minStockLevel: 5,
categoryId: books.id,
createdById: admin.id,
tags: ['programming', 'javascript', 'education']
}
]

for (const productData of products) {
const markup = ((productData.price - productData.cost) / productData.cost) \* 100
await prisma.product.create({
data: {
...productData,
markup
}
})
}
console.log('✅ Products created')

// Create customers
const customers = [
{
firstName: 'Alice',
lastName: 'Johnson',
email: '<EMAIL>',
phone: '******-1111',
loyaltyTier: LoyaltyTier.GOLD,
loyaltyPoints: 5000,
totalSpent: 1200.50,
totalOrders: 8,
averageOrderValue: 150.06,
churnRisk: ChurnRisk.LOW
},
{
firstName: 'Bob',
lastName: 'Smith',
email: '<EMAIL>',
phone: '******-2222',
loyaltyTier: LoyaltyTier.SILVER,
loyaltyPoints: 2500,
totalSpent: 650.00,
totalOrders: 5,
averageOrderValue: 130.00,
churnRisk: ChurnRisk.MEDIUM
},
{
firstName: 'Carol',
lastName: 'Williams',
email: '<EMAIL>',
phone: '******-3333',
loyaltyTier: LoyaltyTier.BRONZE,
loyaltyPoints: 500,
totalSpent: 180.00,
totalOrders: 2,
averageOrderValue: 90.00,
churnRisk: ChurnRisk.HIGH
}
]

for (const customerData of customers) {
await prisma.customer.create({
data: customerData
})
}
console.log('✅ Customers created')

// Create default settings
const defaultSettings = [
{ key: 'business_name', value: 'My POS Store', type: SettingType.STRING },
{ key: 'business_address', value: '123 Main St, City, State 12345', type: SettingType.STRING },
{ key: 'business_phone', value: '******-0000', type: SettingType.STRING },
{ key: 'business_email', value: '<EMAIL>', type: SettingType.STRING },
{ key: 'tax_rate', value: '0.10', type: SettingType.NUMBER },
{ key: 'loyalty_points_per_dollar', value: '1', type: SettingType.NUMBER },
{ key: 'low_stock_threshold', value: '10', type: SettingType.NUMBER },
{ key: 'currency', value: 'USD', type: SettingType.STRING },
{ key: 'timezone', value: 'America/New_York', type: SettingType.STRING },
{ key: 'receipt_footer', value: 'Thank you for shopping with us!', type: SettingType.STRING },
{ key: 'ai_features_enabled', value: 'true', type: SettingType.BOOLEAN },
{ key: 'auto_reorder_enabled', value: 'false', type: SettingType.BOOLEAN }
]

for (const setting of defaultSettings) {
await prisma.setting.upsert({
where: { key: setting.key },
update: {},
create: setting
})
}
console.log('✅ Settings created')

console.log('🎉 Database seed completed!')
console.log('\n📝 Default accounts:')
console.log('Admin: <EMAIL> / admin123')
console.log('Staff: <EMAIL> / staff123')
}

main()
.catch((e) => {
console.error('❌ Seed failed:', e)
process.exit(1)
})
.finally(async () => {
await prisma.$disconnect()
})

// tsconfig.json
{
"compilerOptions": {
"target": "ES2020",
"lib": ["ES2020"],
"module": "commonjs",
"moduleResolution": "node",
"esModuleInterop": true,
"allowSyntheticDefaultImports": true,
"strict": true,
"skipLibCheck": true,
"forceConsistentCasingInFileNames": true,
"resolveJsonModule": true,
"outDir": "./dist",
"rootDir": "./src",
"declaration": true,
"declarationMap": true,
"sourceMap": true,
"removeComments": true
},
"include": [
"src/**/*"
],
"exclude": [
"node_modules",
"dist"
]
}

// .gitignore

# Dependencies

node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log\*

# Environment variables

.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build output

dist/
build/

# Database

_.db
_.sqlite

# Logs

logs/
\*.log

# Runtime data

pids/
_.pid
_.seed
\*.pid.lock

# Coverage directory used by tools like istanbul

coverage/

# IDE files

.vscode/
.idea/
_.swp
_.swo
\*~

# OS generated files

.DS*Store
.DS_Store?
.*\*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
===================

# E-commerce POS Backend - Complete Setup Guide

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or create your project directory
mkdir ecommerce-pos-backend
cd ecommerce-pos-backend

# Initialize package.json (copy from package.json artifact)
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials and API keys
```

### 2. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or create and run migrations (for production)
npm run db:migrate

# Seed database with sample data
npm run db:seed

# Open Prisma Studio to view data
npm run db:studio
```

### 3. Start Development Server

```bash
npm run dev
```

Server will start on `http://localhost:3001`

---

## 📁 Project Structure

```
src/
├── index.ts                 # Main Hono app
├── lib/
│   ├── db.ts               # Prisma client
│   ├── auth.ts             # Auth utilities
│   └── validation.ts       # Zod schemas
├── middleware/
│   ├── auth.ts             # JWT middleware
│   ├── error.ts            # Error handling
│   └── logging.ts          # Audit logging
├── routes/
│   ├── auth.ts             # Authentication
│   ├── users.ts            # User management
│   ├── products.ts         # Product management
│   ├── customers.ts        # Customer/CRM
│   ├── sales.ts            # POS/Sales
│   ├── inventory.ts        # Inventory management
│   ├── suppliers.ts        # Supplier management
│   ├── purchase-orders.ts  # Purchase orders
│   ├── dashboard.ts        # Dashboard data
│   ├── reports.ts          # Analytics/Reports
│   ├── settings.ts         # App settings
│   ├── audit.ts            # Audit logs
│   └── ai.ts               # AI features
└── scripts/
    └── seed.ts             # Database seeding
```

---

## 🔐 API Authentication

All protected endpoints require a JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Login to get token:

```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

---

## 📚 API Endpoints Overview

### Authentication (`/api/auth`)

- `POST /login` - User login
- `POST /register` - Create user (Admin only)
- `GET /me` - Get current user
- `POST /refresh` - Refresh token
- `POST /change-password` - Change password

### Dashboard (`/api/dashboard`)

- `GET /summary` - Main dashboard data
- `GET /recent-sales` - Recent sales list
- `GET /trends` - Sales trends for charts

### Products (`/api/products`)

- `GET /` - List products (with filters)
- `POST /` - Create product
- `GET /:id` - Get single product
- `PUT /:id` - Update product
- `DELETE /:id` - Delete product (soft)
- `GET /barcode/:barcode` - Find by barcode
- `GET /alerts/low-stock` - Low stock products
- `POST /:id/adjust-stock` - Adjust stock levels
- `POST /bulk-update` - Bulk update products

### Customers (`/api/customers`)

- `GET /` - List customers (with filters)
- `POST /` - Create customer
- `GET /:id` - Get customer details
- `PUT /:id` - Update customer
- `DELETE /:id` - Delete customer
- `GET /search/:query` - Search customers
- `POST /:id/loyalty` - Update loyalty points
- `GET /:id/analytics` - Customer analytics
- `GET /analytics/at-risk` - At-risk customers

### Sales/POS (`/api/sales`)

- `GET /` - List sales (with filters)
- `POST /` - Create sale (POS transaction)
- `GET /:id` - Get sale details
- `POST /:id/refund` - Refund sale
- `GET /summary/today` - Today's sales summary
- `GET /analytics/overview` - Sales analytics

### Inventory (`/api/inventory`)

- `GET /overview` - Inventory overview
- `GET /logs` - Inventory movement logs
- `POST /bulk-adjust` - Bulk stock adjustments

### Suppliers (`/api/suppliers`)

- `GET /` - List suppliers
- `POST /` - Create supplier
- `PUT /:id` - Update supplier

### Purchase Orders (`/api/purchase-orders`)

- `GET /` - List purchase orders
- `POST /` - Create purchase order
- `GET /:id` - Get purchase order
- `PATCH /:id/status` - Update PO status
- `POST /:id/receive` - Receive items

### Reports (`/api/reports`)

- `GET /sales` - Sales reports
- `GET /inventory` - Inventory reports
- `GET /customers` - Customer reports

### AI Features (`/api/ai`)

- `POST /generate-description` - Generate product description
- `GET /forecast/sales` - Sales forecast
- `POST /predict-churn` - Predict customer churn
- `GET /recommendations/:customerId` - Product recommendations
- `GET /predict-stock/:productId` - Stock predictions
- `GET /insights` - Business insights

### Settings (`/api/settings`)

- `GET /` - Get all settings
- `PUT /` - Update settings

### Users (`/api/users`) - Admin only

- `GET /` - List users
- `PUT /:id` - Update user
- `DELETE /:id` - Deactivate user

### Audit Logs (`/api/audit`) - Admin only

- `GET /logs` - View audit logs

---

## 🤖 AI Features Configuration

### OpenAI Setup

1. Get API key from OpenAI
2. Add to `.env`: `OPENAI_API_KEY=your-key-here`
3. AI features will automatically be available

### Available AI Features:

- **Product Description Generation**: Auto-generate compelling product descriptions
- **Sales Forecasting**: Predict future sales based on historical data
- **Customer Churn Prediction**: Identify at-risk customers
- **Smart Recommendations**: Suggest products to customers in POS
- **Stock Predictions**: Predict when products will run out
- **Business Insights**: Generate actionable business insights

---

## 🔒 Security Features

### JWT Authentication

- Secure token-based authentication
- Configurable token expiry
- Role-based access control (Admin, Manager, Staff)

### Role Permissions

- **Admin**: Full access to all features
- **Manager**: Cannot manage users or view audit logs
- **Staff**: Basic POS and customer operations

### Audit Logging

- All important actions are logged
- IP address and user agent tracking
- Searchable audit trail

---

## 📊 Database Schema Features

### User Management

- Role-based access control
- Account activation/deactivation
- Password hashing with bcrypt

### Customer Management

- Comprehensive customer profiles
- Loyalty program with tiers
- Churn risk assessment
- Purchase history tracking

### Product Management

- SKU and barcode support
- Category organization
- Stock level tracking with alerts
- Multi-supplier support
- AI-generated descriptions

### Sales & POS

- Full transaction processing
- Multiple payment methods
- Loyalty points integration
- Refund handling
- Real-time inventory updates

### Inventory Management

- Real-time stock tracking
- Movement history logs
- Low stock alerts
- Purchase order integration
- Bulk operations

### Reporting & Analytics

- Sales performance tracking
- Customer analytics
- Inventory reports
- Trend analysis
- AI-powered insights

---

## 🚀 Production Deployment

### Environment Variables

```bash
DATABASE_URL="your-production-db-url"
JWT_SECRET="your-super-secret-production-key"
JWT_EXPIRES_IN="7d"
OPENAI_API_KEY="your-openai-key"
PORT=3001
NODE_ENV="production"
```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "start"]
```

### Database Migration

```bash
# Run migrations in production
npm run db:migrate

# Don't forget to seed initial admin user
npm run db:seed
```

---

## 📝 Sample Frontend Integration

### Login Example

```typescript
const login = async (email: string, password: string) => {
  const response = await fetch("/api/auth/login", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password }),
  });

  const data = await response.json();

  if (data.token) {
    localStorage.setItem("token", data.token);
    return data.user;
  }
};
```

### API Client Example

```typescript
const apiClient = {
  token: localStorage.getItem("token"),

  async request(endpoint: string, options: RequestInit = {}) {
    return fetch(`/api${endpoint}`, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
    });
  },

  // Usage examples
  getProducts: () => apiClient.request("/products"),
  createSale: (saleData) =>
    apiClient.request("/sales", {
      method: "POST",
      body: JSON.stringify(saleData),
    }),
};
```

---

## 🛠️ Development Tips

### Database Schema Changes

```bash
# After modifying schema.prisma
npm run db:generate
npm run db:migrate
```

### Adding New Routes

1. Create route file in `src/routes/`
2. Import and mount in `src/index.ts`
3. Add appropriate middleware
4. Update API documentation

### Custom AI Features

Extend the AI routes to add your custom AI functionality:

```typescript
// In src/routes/ai.ts
aiRoutes.post("/custom-feature", async (c) => {
  // Your custom AI logic here
  const result = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: "Your prompt" }],
  });

  return c.json({ result: result.choices[0]?.message?.content });
});
```

---

## 🔧 Troubleshooting

### Common Issues

**Database Connection Errors**

- Verify DATABASE_URL is correct
- Ensure PostgreSQL is running
- Check firewall settings

**JWT Token Issues**

- Verify JWT_SECRET is set
- Check token expiry settings
- Ensure proper header format

**OpenAI API Errors**

- Verify API key is valid
- Check API usage limits
- Handle rate limiting in production

**Low Stock Alerts Not Working**

- Verify min_stock_level is set on products
- Check if background jobs are running
- Verify AI prediction cron jobs

---

# This backend provides a complete foundation for your Next.js E-commerce POS frontend. All the AI features, user management, inventory tracking, and POS functionality are ready to integrate with your frontend components!
